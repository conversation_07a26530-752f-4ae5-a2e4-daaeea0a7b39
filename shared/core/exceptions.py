#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一异常处理模块

提供项目的统一异常处理功能：
- 自定义异常类型
- 异常处理装饰器
- 错误码管理
- 重试机制
- 异常监控和报告

作者: User-DF Team
版本: 2.0.0
"""

import functools
import time
import logging
from typing import Dict, Any, Optional, Callable, Type, Union, List
from dataclasses import dataclass
from enum import Enum
import traceback
import threading


class ErrorCode(Enum):
    """错误码枚举"""
    # 通用错误
    UNKNOWN_ERROR = (1000, "未知错误")
    INVALID_PARAMETER = (1001, "参数无效")
    CONFIGURATION_ERROR = (1002, "配置错误")
    PERMISSION_DENIED = (1003, "权限不足")
    
    # 数据库错误
    DATABASE_CONNECTION_ERROR = (2000, "数据库连接错误")
    DATABASE_QUERY_ERROR = (2001, "数据库查询错误")
    DATABASE_WRITE_ERROR = (2002, "数据库写入错误")
    DATABASE_TIMEOUT = (2003, "数据库操作超时")
    
    # MongoDB特定错误
    MONGODB_CONNECTION_ERROR = (2100, "MongoDB连接错误")
    MONGODB_COLLECTION_NOT_FOUND = (2101, "MongoDB集合不存在")
    MONGODB_DOCUMENT_NOT_FOUND = (2102, "MongoDB文档不存在")
    MONGODB_DUPLICATE_KEY = (2103, "MongoDB重复键错误")
    
    # Milvus特定错误
    MILVUS_CONNECTION_ERROR = (2200, "Milvus连接错误")
    MILVUS_COLLECTION_NOT_FOUND = (2201, "Milvus集合不存在")
    MILVUS_VECTOR_DIMENSION_ERROR = (2202, "向量维度错误")
    MILVUS_QUERY_ERROR = (2203, "Milvus查询错误")
    
    # 数据处理错误
    DATA_VALIDATION_ERROR = (3000, "数据验证错误")
    DATA_CONVERSION_ERROR = (3001, "数据转换错误")
    DATA_FORMAT_ERROR = (3002, "数据格式错误")
    DATA_SIZE_ERROR = (3003, "数据大小错误")
    
    # 文件操作错误
    FILE_NOT_FOUND = (4000, "文件不存在")
    FILE_PERMISSION_ERROR = (4001, "文件权限错误")
    FILE_FORMAT_ERROR = (4002, "文件格式错误")
    FILE_SIZE_ERROR = (4003, "文件大小错误")
    
    # 网络错误
    NETWORK_CONNECTION_ERROR = (5000, "网络连接错误")
    NETWORK_TIMEOUT = (5001, "网络超时")
    HTTP_ERROR = (5002, "HTTP请求错误")
    API_ERROR = (5003, "API调用错误")
    
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    delay: float = 1.0
    backoff_multiplier: float = 2.0
    max_delay: float = 60.0
    exceptions: tuple = (Exception,)


class UserDFException(Exception):
    """User-DF项目基础异常类"""
    
    def __init__(self, 
                 message: str,
                 error_code: Optional[ErrorCode] = None,
                 details: Optional[Dict[str, Any]] = None,
                 cause: Optional[Exception] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误码
            details: 错误详情
            cause: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or ErrorCode.UNKNOWN_ERROR
        self.details = details or {}
        self.cause = cause
        self.timestamp = time.time()
        self.thread_id = threading.get_ident()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "message": self.message,
            "error_code": self.error_code.code,
            "error_message": self.error_code.message,
            "details": self.details,
            "timestamp": self.timestamp,
            "thread_id": self.thread_id,
            "traceback": traceback.format_exc() if self.cause else None
        }
    
    def __str__(self) -> str:
        return f"[{self.error_code.code}] {self.message}"


class DatabaseException(UserDFException):
    """数据库异常"""
    pass


class MongoDBException(DatabaseException):
    """MongoDB异常"""
    pass


class MilvusException(DatabaseException):
    """Milvus异常"""
    pass


class DataValidationException(UserDFException):
    """数据验证异常"""
    pass


class ConfigurationException(UserDFException):
    """配置异常"""
    pass


class NetworkException(UserDFException):
    """网络异常"""
    pass


class ExceptionHandler:
    """异常处理器"""
    
    _logger = logging.getLogger(__name__)
    _error_stats: Dict[str, int] = {}
    _lock = threading.Lock()
    
    @classmethod
    def handle_exception(cls, 
                        exception: Exception,
                        context: Optional[Dict[str, Any]] = None,
                        reraise: bool = True) -> Optional[UserDFException]:
        """
        处理异常
        
        Args:
            exception: 原始异常
            context: 异常上下文信息
            reraise: 是否重新抛出异常
            
        Returns:
            处理后的异常（如果不重新抛出）
        """
        # 统计错误
        cls._update_error_stats(exception)
        
        # 转换为UserDF异常
        if isinstance(exception, UserDFException):
            user_df_exception = exception
        else:
            user_df_exception = cls._convert_to_user_df_exception(exception, context)
        
        # 记录日志
        cls._log_exception(user_df_exception, context)
        
        if reraise:
            raise user_df_exception
        else:
            return user_df_exception
    
    @classmethod
    def _convert_to_user_df_exception(cls, 
                                     exception: Exception,
                                     context: Optional[Dict[str, Any]] = None) -> UserDFException:
        """将普通异常转换为UserDF异常"""
        error_code = cls._determine_error_code(exception)
        
        return UserDFException(
            message=str(exception),
            error_code=error_code,
            details=context or {},
            cause=exception
        )
    
    @classmethod
    def _determine_error_code(cls, exception: Exception) -> ErrorCode:
        """根据异常类型确定错误码"""
        exception_type = type(exception).__name__
        
        # MongoDB相关异常
        if 'mongo' in exception_type.lower() or 'pymongo' in exception_type.lower():
            if 'connection' in str(exception).lower():
                return ErrorCode.MONGODB_CONNECTION_ERROR
            elif 'duplicate' in str(exception).lower():
                return ErrorCode.MONGODB_DUPLICATE_KEY
            else:
                return ErrorCode.DATABASE_QUERY_ERROR
        
        # Milvus相关异常
        elif 'milvus' in exception_type.lower():
            if 'connection' in str(exception).lower():
                return ErrorCode.MILVUS_CONNECTION_ERROR
            elif 'collection' in str(exception).lower():
                return ErrorCode.MILVUS_COLLECTION_NOT_FOUND
            else:
                return ErrorCode.MILVUS_QUERY_ERROR
        
        # 文件相关异常
        elif isinstance(exception, FileNotFoundError):
            return ErrorCode.FILE_NOT_FOUND
        elif isinstance(exception, PermissionError):
            return ErrorCode.FILE_PERMISSION_ERROR
        
        # 网络相关异常
        elif 'connection' in exception_type.lower() or 'timeout' in exception_type.lower():
            return ErrorCode.NETWORK_CONNECTION_ERROR
        
        # 参数相关异常
        elif isinstance(exception, (ValueError, TypeError)):
            return ErrorCode.INVALID_PARAMETER
        
        else:
            return ErrorCode.UNKNOWN_ERROR
    
    @classmethod
    def _log_exception(cls, exception: UserDFException, context: Optional[Dict[str, Any]] = None):
        """记录异常日志"""
        log_data = {
            "exception_type": type(exception).__name__,
            "error_code": exception.error_code.code,
            "message": exception.message,
            "details": exception.details,
            "context": context or {},
            "traceback": traceback.format_exc()
        }
        
        cls._logger.error(f"异常处理: {exception}", extra={"extra_fields": log_data})
    
    @classmethod
    def _update_error_stats(cls, exception: Exception):
        """更新错误统计"""
        with cls._lock:
            exception_type = type(exception).__name__
            cls._error_stats[exception_type] = cls._error_stats.get(exception_type, 0) + 1
    
    @classmethod
    def get_error_stats(cls) -> Dict[str, int]:
        """获取错误统计"""
        with cls._lock:
            return cls._error_stats.copy()
    
    @classmethod
    def clear_error_stats(cls):
        """清除错误统计"""
        with cls._lock:
            cls._error_stats.clear()


def retry(config: Optional[RetryConfig] = None):
    """
    重试装饰器
    
    Args:
        config: 重试配置
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            delay = config.delay
            
            for attempt in range(config.max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt < config.max_retries:
                        logging.getLogger(__name__).warning(
                            f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, "
                            f"{delay:.2f}秒后重试"
                        )
                        time.sleep(delay)
                        delay = min(delay * config.backoff_multiplier, config.max_delay)
                    else:
                        logging.getLogger(__name__).error(
                            f"函数 {func.__name__} 重试 {config.max_retries} 次后仍然失败"
                        )
                        break
            
            # 处理最终异常
            ExceptionHandler.handle_exception(
                last_exception,
                context={"function": func.__name__, "attempts": config.max_retries + 1}
            )
        
        return wrapper
    return decorator


def exception_handler(reraise: bool = True, context: Optional[Dict[str, Any]] = None):
    """
    异常处理装饰器
    
    Args:
        reraise: 是否重新抛出异常
        context: 异常上下文信息
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_context = context or {}
                func_context.update({
                    "function": func.__name__,
                    "args": str(args)[:200],  # 限制长度
                    "kwargs": str(kwargs)[:200]
                })
                
                return ExceptionHandler.handle_exception(e, func_context, reraise)
        
        return wrapper
    return decorator
