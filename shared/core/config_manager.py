#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器

提供项目的统一配置管理功能：
- 支持多环境配置（development, testing, production）
- 支持配置文件热重载
- 支持配置验证和默认值
- 支持环境变量覆盖
- 支持配置缓存和性能优化

作者: User-DF Team
版本: 2.0.0
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass
import logging
from threading import Lock
import time

logger = logging.getLogger(__name__)


@dataclass
class ConfigInfo:
    """配置信息数据类"""
    name: str
    path: Path
    last_modified: float
    content: Dict[str, Any]


class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    _lock = Lock()
    
    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, config_dir: Union[str, Path] = "configs", config_file: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录路径
            config_file: 指定的配置文件路径（如果提供，将从该文件加载所有配置）
        """
        if hasattr(self, '_initialized'):
            return
            
        self.config_dir = Path(config_dir)
        self._configs: Dict[str, ConfigInfo] = {}
        self._environment = os.getenv('USER_DF_ENV', 'development')
        self._cache_enabled = True
        self._auto_reload = True
        self._reload_interval = 60  # 秒
        self._last_check_time = 0

        # 优先使用环境变量中的配置文件路径
        env_config_file = os.getenv('USER_DF_CONFIG_FILE')
        if env_config_file:
            self._config_file = Path(env_config_file)
        elif config_file:
            self._config_file = Path(config_file)
        else:
            self._config_file = None
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 如果指定了配置文件，从该文件加载所有配置
        if self._config_file:
            self._load_from_single_file()
        # 注意：不再自动加载全局配置，各服务应使用独立的配置文件
        
        self._initialized = True
        logger.info(f"配置管理器初始化完成，环境: {self._environment}")
    
    def _load_from_single_file(self):
        """从单个配置文件加载所有配置"""
        if not self._config_file.exists():
            raise FileNotFoundError(f"指定的配置文件 {self._config_file} 不存在")

        try:
            with open(self._config_file, 'r', encoding='utf-8') as f:
                all_config = yaml.safe_load(f) or {}

            # 从配置文件路径推断服务名称
            service_name = self._infer_service_name_from_path()

            # 将配置分解为不同的模块
            # 全局配置
            global_config = {
                "project": all_config.get("project", {}),
                "cache": all_config.get("cache", {}),
                "monitoring": all_config.get("monitoring", {})
            }
            self._configs["global"] = ConfigInfo(
                name="global",
                path=self._config_file,
                last_modified=self._config_file.stat().st_mtime,
                content=global_config
            )

            # MongoDB配置
            mongodb_config = all_config.get("mongodb", {})
            self._configs["mongodb"] = ConfigInfo(
                name="mongodb",
                path=self._config_file,
                last_modified=self._config_file.stat().st_mtime,
                content=mongodb_config
            )

            # Milvus配置
            milvus_config = all_config.get("milvus", {})
            self._configs["milvus"] = ConfigInfo(
                name="milvus",
                path=self._config_file,
                last_modified=self._config_file.stat().st_mtime,
                content=milvus_config
            )

            # 服务配置（除了mongodb和milvus之外的所有配置）
            service_config = {k: v for k, v in all_config.items()
                            if k not in ["mongodb", "milvus", "project", "cache", "monitoring", "logging"]}
            self._configs[service_name] = ConfigInfo(
                name=service_name,
                path=self._config_file,
                last_modified=self._config_file.stat().st_mtime,
                content=service_config
            )

            # 日志配置
            logging_config = all_config.get("logging", {})
            if logging_config:
                self._configs["logging"] = ConfigInfo(
                    name="logging",
                    path=self._config_file,
                    last_modified=self._config_file.stat().st_mtime,
                    content=logging_config
                )

            # 预加载第一个服务配置以获取共享配置（如logging）
            self._preload_first_service_config(all_config)

            logger.info(f"从单个配置文件加载完成: {self._config_file}, 服务名称: {service_name}")

        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"配置文件 {self._config_file} 格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件 {self._config_file} 失败: {e}")

    def _infer_service_name_from_path(self) -> str:
        """从配置文件路径推断服务名称"""
        if not self._config_file:
            return "unknown_service"

        # 尝试从路径中提取服务名称
        # 例如: configs/orc_mongodb_service/orc_processor_service/production.yaml -> orc_processor_service
        # 例如: configs/user_vector_service/production.yaml -> user_vector_service
        path_parts = self._config_file.parts

        # 查找 configs 目录的位置
        try:
            configs_index = path_parts.index("configs")

            # 如果是三级结构：configs/main_service/sub_service/config.yaml
            if configs_index + 3 < len(path_parts):
                service_name = path_parts[configs_index + 2]  # 取子服务名称
                return service_name
            # 如果是二级结构：configs/service/config.yaml
            elif configs_index + 2 < len(path_parts):
                service_name = path_parts[configs_index + 1]  # 取服务名称
                return service_name
        except ValueError:
            pass

        # 如果无法从路径推断，尝试从文件名推断
        # 例如: user_vector_service.yaml -> user_vector_service
        filename = self._config_file.stem
        if filename not in ["development", "production", "testing"]:
            return filename

        # 默认返回通用服务名称
        return "service"

    def _try_load_service_config_from_reference(self, service_name: str) -> bool:
        """
        尝试从服务配置文件引用中加载配置

        Args:
            service_name: 服务名称

        Returns:
            是否成功加载配置
        """
        try:
            # 检查是否有主配置文件中的服务引用
            main_service_name = self._infer_service_name_from_path()
            if main_service_name in self._configs:
                main_config = self._configs[main_service_name].content

                # 查找服务配置文件路径
                services_config = main_config.get("services", {})
                service_config = services_config.get(service_name, {})
                config_file_path = service_config.get("config_file")

                if config_file_path:
                    # 加载引用的配置文件
                    config_file_path = Path(config_file_path)
                    if not config_file_path.is_absolute():
                        # 相对路径，相对于项目根目录
                        config_file_path = Path.cwd() / config_file_path

                    if config_file_path.exists():
                        with open(config_file_path, 'r', encoding='utf-8') as f:
                            service_config_content = yaml.safe_load(f) or {}

                        # 缓存配置
                        self._configs[service_name] = ConfigInfo(
                            name=service_name,
                            path=config_file_path,
                            last_modified=config_file_path.stat().st_mtime,
                            content=service_config_content
                        )

                        # 提取并缓存共享配置（如logging、hive等）
                        self._extract_shared_configs_from_service(service_config_content, config_file_path)

                        logger.info(f"从引用配置文件加载 {service_name}: {config_file_path}")
                        return True
                    else:
                        logger.warning(f"引用的配置文件不存在: {config_file_path}")

            # 如果直接服务配置加载失败，尝试从已加载的服务配置中查找共享配置
            if self._try_load_shared_config_from_existing_services(service_name):
                return True

        except Exception as e:
            logger.debug(f"从引用加载服务配置失败 {service_name}: {e}")

        return False

    def _extract_shared_configs_from_service(self, service_config: Dict[str, Any], config_path: Path):
        """
        从服务配置中提取共享配置

        Args:
            service_config: 服务配置内容
            config_path: 配置文件路径
        """
        shared_config_keys = ["logging", "redis", "monitoring"]
        last_modified = config_path.stat().st_mtime

        for key in shared_config_keys:
            if key in service_config and key not in self._configs:
                self._configs[key] = ConfigInfo(
                    name=key,
                    path=config_path,
                    last_modified=last_modified,
                    content=service_config[key]
                )
                logger.debug(f"从服务配置中提取共享配置 {key}")

    def _try_load_shared_config_from_existing_services(self, config_name: str) -> bool:
        """
        尝试从已加载的服务配置中查找共享配置

        Args:
            config_name: 配置名称

        Returns:
            是否成功找到配置
        """
        shared_config_keys = ["logging", "redis", "monitoring"]

        if config_name not in shared_config_keys:
            return False

        # 遍历已加载的服务配置，查找共享配置
        for service_name, config_info in self._configs.items():
            if service_name.endswith("_service") and config_name in config_info.content:
                self._configs[config_name] = ConfigInfo(
                    name=config_name,
                    path=config_info.path,
                    last_modified=config_info.last_modified,
                    content=config_info.content[config_name]
                )
                logger.info(f"从已加载的服务配置 {service_name} 中找到共享配置 {config_name}")
                return True

        return False

    def _preload_first_service_config(self, main_config: Dict[str, Any]):
        """
        预加载服务配置以获取共享配置

        Args:
            main_config: 主配置文件内容
        """
        try:
            services_config = main_config.get("services", {})
            if not services_config:
                return

            shared_config_keys = ["logging", "hive", "redis", "monitoring"]
            found_configs = set()

            # 遍历所有服务配置，查找共享配置
            for service_name, service_config in services_config.items():
                config_file_path = service_config.get("config_file")

                if config_file_path:
                    config_file_path = Path(config_file_path)
                    if not config_file_path.is_absolute():
                        config_file_path = Path.cwd() / config_file_path

                    if config_file_path.exists():
                        with open(config_file_path, 'r', encoding='utf-8') as f:
                            service_config_content = yaml.safe_load(f) or {}

                        # 提取共享配置
                        self._extract_shared_configs_from_service(service_config_content, config_file_path)

                        # 记录找到的配置
                        for key in shared_config_keys:
                            if key in service_config_content:
                                found_configs.add(key)

                        logger.debug(f"预加载服务配置 {service_name} 以获取共享配置")

                        # 如果已经找到所有共享配置，可以提前退出
                        if len(found_configs) == len(shared_config_keys):
                            break

        except Exception as e:
            logger.debug(f"预加载服务配置失败: {e}")

    def _load_global_config(self):
        """
        加载全局配置（已弃用）

        注意：此方法已弃用，各服务应使用独立的配置文件。
        为了向后兼容，保留此方法但不执行任何操作。
        """
        # 不再自动加载全局配置文件
        # 各服务应在自己的配置文件中包含所有必要的配置项
        pass
    
    def load_config(self, name: str, config_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            name: 配置名称
            config_path: 配置文件路径，如果不提供则使用默认路径
            
        Returns:
            配置字典
            
        Raises:
            FileNotFoundError: 配置文件不存在
            yaml.YAMLError: 配置文件格式错误
        """
        # 如果使用单个配置文件模式，直接返回已加载的配置
        if self._config_file and name in self._configs:
            return self._configs[name].content
        
        if config_path is None:
            # 首先尝试服务特定的配置文件路径
            service_config_path = self.config_dir / name / f"{self._environment}.yaml"
            if service_config_path.exists():
                config_path = service_config_path
            else:
                # 回退到传统的配置文件路径
                config_path = self.config_dir / f"{name}.yaml"
        else:
            config_path = Path(config_path)
        
        # 检查文件是否存在
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        
        # 检查是否需要重新加载
        last_modified = config_path.stat().st_mtime
        if (name in self._configs and 
            self._configs[name].last_modified >= last_modified and
            self._cache_enabled):
            return self._configs[name].content
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = yaml.safe_load(f) or {}
            
            # 应用环境特定配置（仅在非单文件模式下）
            if not self._config_file:
                content = self._apply_environment_config(content, name)

            # 应用环境变量覆盖
            content = self._apply_env_overrides(content, name)

            # 缓存配置
            self._configs[name] = ConfigInfo(
                name=name,
                path=config_path,
                last_modified=last_modified,
                content=content
            )

            # 如果这是一个服务配置文件，同时缓存其中的子配置
            if name in ["orc_mongodb_service", "user_vector_service"] and isinstance(content, dict):
                # 缓存 mongodb 配置
                if "mongodb" in content:
                    self._configs["mongodb"] = ConfigInfo(
                        name="mongodb",
                        path=config_path,
                        last_modified=last_modified,
                        content=content["mongodb"]
                    )

                # 缓存 milvus 配置
                if "milvus" in content:
                    self._configs["milvus"] = ConfigInfo(
                        name="milvus",
                        path=config_path,
                        last_modified=last_modified,
                        content=content["milvus"]
                    )

                # 缓存 logging 配置
                if "logging" in content:
                    self._configs["logging"] = ConfigInfo(
                        name="logging",
                        path=config_path,
                        last_modified=last_modified,
                        content=content["logging"]
                    )

            logger.debug(f"配置 {name} 加载成功: {config_path}")
            return content
            
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"配置文件 {config_path} 格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件 {config_path} 失败: {e}")
    
    def _apply_environment_config(self, config: Dict[str, Any], name: str) -> Dict[str, Any]:
        """应用环境特定配置"""
        env_config_path = self.config_dir / "environments" / f"{self._environment}.yaml"
        if env_config_path.exists():
            try:
                with open(env_config_path, 'r', encoding='utf-8') as f:
                    env_config = yaml.safe_load(f) or {}
                
                # 合并环境特定配置
                if name in env_config:
                    config = self._deep_merge(config, env_config[name])
                    
            except Exception as e:
                logger.warning(f"加载环境配置失败: {e}")
        
        return config
    
    def _apply_env_overrides(self, config: Dict[str, Any], name: str) -> Dict[str, Any]:
        """应用环境变量覆盖"""
        prefix = f"USER_DF_{name.upper()}_"
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower().replace('_', '.')
                self._set_nested_value(config, config_key, value)
        
        return config
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _set_nested_value(self, config: Dict[str, Any], key_path: str, value: str):
        """设置嵌套配置值"""
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换值类型
        try:
            if value.lower() in ('true', 'false'):
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
            elif '.' in value and value.replace('.', '').isdigit():
                value = float(value)
        except:
            pass  # 保持字符串类型
        
        current[keys[-1]] = value
    
    def _save_config(self, name: str, config_path: Path, content: Dict[str, Any]):
        """保存配置到文件"""
        try:
            config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(content, f, default_flow_style=False, allow_unicode=True)
            logger.debug(f"配置 {name} 保存成功: {config_path}")
        except Exception as e:
            logger.error(f"保存配置文件 {config_path} 失败: {e}")
            raise

    def get_config(self, name: str, key_path: Optional[str] = None, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            name: 配置名称
            key_path: 配置键路径，支持点分隔的嵌套路径，如 'database.mongodb.host'
            default: 默认值

        Returns:
            配置值
        """
        if name not in self._configs:
            try:
                # 首先尝试直接加载配置
                self.load_config(name)
            except FileNotFoundError:
                # 如果直接加载失败，检查是否有服务配置文件引用
                if self._config_file and self._try_load_service_config_from_reference(name):
                    # 成功从引用加载配置
                    pass
                else:
                    logger.warning(f"配置 {name} 不存在，返回默认值")
                    return default

        config = self._configs[name].content

        if key_path is None:
            return config

        # 解析嵌套路径
        keys = key_path.split('.')
        current = config

        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            logger.debug(f"配置路径 {name}.{key_path} 不存在，返回默认值")
            return default

    def set_config(self, name: str, key_path: str, value: Any, save: bool = False):
        """
        设置配置值

        Args:
            name: 配置名称
            key_path: 配置键路径
            value: 配置值
            save: 是否保存到文件
        """
        if name not in self._configs:
            self.load_config(name)

        config = self._configs[name].content
        keys = key_path.split('.')
        current = config

        # 创建嵌套结构
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]

        current[keys[-1]] = value

        # 更新缓存
        self._configs[name].content = config

        if save:
            self._save_config(name, self._configs[name].path, config)

        logger.debug(f"配置 {name}.{key_path} 设置为: {value}")

    def reload_config(self, name: Optional[str] = None):
        """
        重新加载配置

        Args:
            name: 配置名称，如果为None则重新加载所有配置
        """
        if name is None:
            # 重新加载所有配置
            config_names = list(self._configs.keys())
            for config_name in config_names:
                try:
                    self.load_config(config_name, self._configs[config_name].path)
                    logger.info(f"配置 {config_name} 重新加载成功")
                except Exception as e:
                    logger.error(f"重新加载配置 {config_name} 失败: {e}")
        else:
            if name in self._configs:
                try:
                    self.load_config(name, self._configs[name].path)
                    logger.info(f"配置 {name} 重新加载成功")
                except Exception as e:
                    logger.error(f"重新加载配置 {name} 失败: {e}")
            else:
                logger.warning(f"配置 {name} 未加载，无法重新加载")

    def check_and_reload(self):
        """检查并重新加载已修改的配置文件"""
        if not self._auto_reload:
            return

        current_time = time.time()
        if current_time - self._last_check_time < self._reload_interval:
            return

        self._last_check_time = current_time

        for name, config_info in self._configs.items():
            try:
                if config_info.path.exists():
                    last_modified = config_info.path.stat().st_mtime
                    if last_modified > config_info.last_modified:
                        self.load_config(name, config_info.path)
                        logger.info(f"检测到配置文件变化，自动重新加载: {name}")
            except Exception as e:
                logger.error(f"检查配置文件 {name} 时发生错误: {e}")

    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已加载的配置"""
        return {name: config.content for name, config in self._configs.items()}

    def validate_config(self, name: str, schema: Dict[str, Any]) -> bool:
        """
        验证配置格式

        Args:
            name: 配置名称
            schema: 配置模式

        Returns:
            验证结果
        """
        # 这里可以集成jsonschema等验证库
        # 暂时返回True，后续可以扩展
        return True

    @property
    def environment(self) -> str:
        """获取当前环境"""
        return self._environment

    @environment.setter
    def environment(self, env: str):
        """设置当前环境"""
        self._environment = env
        logger.info(f"环境设置为: {env}")
        # 重新加载配置以应用新环境（仅在非单文件模式下）
        if not self._config_file:
            self.reload_config()
    
    @property
    def config_file(self) -> Optional[Path]:
        """获取当前使用的配置文件路径"""
        return self._config_file
    
    def is_single_file_mode(self) -> bool:
        """检查是否为单文件配置模式"""
        return self._config_file is not None

    def enable_cache(self, enabled: bool = True):
        """启用或禁用配置缓存"""
        self._cache_enabled = enabled
        logger.info(f"配置缓存: {'启用' if enabled else '禁用'}")

    def enable_auto_reload(self, enabled: bool = True, interval: int = 60):
        """启用或禁用自动重新加载"""
        self._auto_reload = enabled
        self._reload_interval = interval
        logger.info(f"自动重新加载: {'启用' if enabled else '禁用'}, 间隔: {interval}秒")
    
    @classmethod
    def create_from_config_file(cls, config_file: Union[str, Path]) -> 'ConfigManager':
        """从指定配置文件创建配置管理器实例"""
        # 重置单例实例以支持不同的配置文件
        cls._instance = None
        return cls(config_file=config_file)
