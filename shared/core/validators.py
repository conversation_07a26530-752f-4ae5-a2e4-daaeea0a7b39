#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证器模块

提供项目的统一数据验证功能：
- 基础数据类型验证
- 业务数据验证
- 配置数据验证
- 向量数据验证
- 批量数据验证

作者: User-DF Team
版本: 2.0.0
"""

import re
from typing import Any, Dict, List, Optional, Union, Callable, Type
from dataclasses import dataclass
from datetime import datetime
import numpy as np
from pathlib import Path

from .exceptions import DataValidationException, ErrorCode


@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    validator: Callable[[Any], bool]
    error_message: str
    required: bool = True


class DataValidator:
    """数据验证器"""
    
    # 预定义的验证规则
    VALIDATION_RULES = {
        # 基础类型验证
        'string': ValidationRule(
            name='string',
            validator=lambda x: isinstance(x, str),
            error_message='必须是字符串类型'
        ),
        'integer': ValidationRule(
            name='integer',
            validator=lambda x: isinstance(x, int),
            error_message='必须是整数类型'
        ),
        'float': ValidationRule(
            name='float',
            validator=lambda x: isinstance(x, (int, float)),
            error_message='必须是数字类型'
        ),
        'boolean': ValidationRule(
            name='boolean',
            validator=lambda x: isinstance(x, bool),
            error_message='必须是布尔类型'
        ),
        'list': ValidationRule(
            name='list',
            validator=lambda x: isinstance(x, list),
            error_message='必须是列表类型'
        ),
        'dict': ValidationRule(
            name='dict',
            validator=lambda x: isinstance(x, dict),
            error_message='必须是字典类型'
        ),
        
        # 数值范围验证
        'positive_integer': ValidationRule(
            name='positive_integer',
            validator=lambda x: isinstance(x, int) and x > 0,
            error_message='必须是正整数'
        ),
        'non_negative_integer': ValidationRule(
            name='non_negative_integer',
            validator=lambda x: isinstance(x, int) and x >= 0,
            error_message='必须是非负整数'
        ),
        'positive_float': ValidationRule(
            name='positive_float',
            validator=lambda x: isinstance(x, (int, float)) and x > 0,
            error_message='必须是正数'
        ),
        'non_negative_float': ValidationRule(
            name='non_negative_float',
            validator=lambda x: isinstance(x, (int, float)) and x >= 0,
            error_message='必须是非负数'
        ),
        
        # 字符串格式验证
        'non_empty_string': ValidationRule(
            name='non_empty_string',
            validator=lambda x: isinstance(x, str) and len(x.strip()) > 0,
            error_message='字符串不能为空'
        ),
        'email': ValidationRule(
            name='email',
            validator=lambda x: isinstance(x, str) and re.match(r'^[^@]+@[^@]+\.[^@]+$', x),
            error_message='邮箱格式无效'
        ),
        'url': ValidationRule(
            name='url',
            validator=lambda x: isinstance(x, str) and re.match(r'^https?://.+', x),
            error_message='URL格式无效'
        ),
        'date_string': ValidationRule(
            name='date_string',
            validator=lambda x: isinstance(x, str) and re.match(r'^\d{8}$', x),
            error_message='日期格式必须是YYYYMMDD'
        ),
        
        # 业务特定验证
        'uid': ValidationRule(
            name='uid',
            validator=lambda x: isinstance(x, int) and 0 <= x <= 2000000000,
            error_message='UID必须是0到20亿之间的整数'
        ),
        'pid': ValidationRule(
            name='pid',
            validator=lambda x: isinstance(x, str) and len(x) > 0,
            error_message='PID必须是非空字符串'
        ),
        'vector_512d': ValidationRule(
            name='vector_512d',
            validator=lambda x: isinstance(x, (list, np.ndarray)) and len(x) == 512,
            error_message='向量必须是512维'
        ),
        'vector_256d': ValidationRule(
            name='vector_256d',
            validator=lambda x: isinstance(x, (list, np.ndarray)) and len(x) == 256,
            error_message='向量必须是256维'
        ),
    }
    
    @classmethod
    def validate(cls, data: Any, rules: Union[str, List[str], Dict[str, Any]], 
                field_name: str = "data") -> bool:
        """
        验证数据
        
        Args:
            data: 要验证的数据
            rules: 验证规则，可以是规则名称、规则列表或规则字典
            field_name: 字段名称，用于错误消息
            
        Returns:
            验证结果
            
        Raises:
            DataValidationException: 验证失败时抛出
        """
        if isinstance(rules, str):
            # 单个规则
            return cls._validate_single_rule(data, rules, field_name)
        elif isinstance(rules, list):
            # 多个规则
            for rule in rules:
                cls._validate_single_rule(data, rule, field_name)
            return True
        elif isinstance(rules, dict):
            # 复杂验证规则
            return cls._validate_complex_rules(data, rules, field_name)
        else:
            raise DataValidationException(
                f"不支持的验证规则类型: {type(rules)}",
                ErrorCode.INVALID_PARAMETER
            )
    
    @classmethod
    def _validate_single_rule(cls, data: Any, rule_name: str, field_name: str) -> bool:
        """验证单个规则"""
        if rule_name not in cls.VALIDATION_RULES:
            raise DataValidationException(
                f"未知的验证规则: {rule_name}",
                ErrorCode.INVALID_PARAMETER
            )
        
        rule = cls.VALIDATION_RULES[rule_name]
        
        if not rule.validator(data):
            raise DataValidationException(
                f"字段 '{field_name}' 验证失败: {rule.error_message}",
                ErrorCode.DATA_VALIDATION_ERROR,
                details={"field": field_name, "rule": rule_name, "value": str(data)[:100]}
            )
        
        return True
    
    @classmethod
    def _validate_complex_rules(cls, data: Any, rules: Dict[str, Any], field_name: str) -> bool:
        """验证复杂规则"""
        # 类型验证
        if 'type' in rules:
            cls._validate_single_rule(data, rules['type'], field_name)
        
        # 范围验证
        if 'min' in rules:
            if not (isinstance(data, (int, float)) and data >= rules['min']):
                raise DataValidationException(
                    f"字段 '{field_name}' 值 {data} 小于最小值 {rules['min']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        if 'max' in rules:
            if not (isinstance(data, (int, float)) and data <= rules['max']):
                raise DataValidationException(
                    f"字段 '{field_name}' 值 {data} 大于最大值 {rules['max']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        # 长度验证
        if 'min_length' in rules:
            if not (hasattr(data, '__len__') and len(data) >= rules['min_length']):
                raise DataValidationException(
                    f"字段 '{field_name}' 长度 {len(data) if hasattr(data, '__len__') else 'N/A'} "
                    f"小于最小长度 {rules['min_length']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        if 'max_length' in rules:
            if not (hasattr(data, '__len__') and len(data) <= rules['max_length']):
                raise DataValidationException(
                    f"字段 '{field_name}' 长度 {len(data) if hasattr(data, '__len__') else 'N/A'} "
                    f"大于最大长度 {rules['max_length']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        # 正则表达式验证
        if 'pattern' in rules:
            if not (isinstance(data, str) and re.match(rules['pattern'], data)):
                raise DataValidationException(
                    f"字段 '{field_name}' 不匹配模式 {rules['pattern']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        # 枚举值验证
        if 'choices' in rules:
            if data not in rules['choices']:
                raise DataValidationException(
                    f"字段 '{field_name}' 值 {data} 不在允许的选择中: {rules['choices']}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        return True
    
    @classmethod
    def validate_dict(cls, data: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """
        验证字典数据
        
        Args:
            data: 要验证的字典数据
            schema: 验证模式
            
        Returns:
            验证结果
        """
        if not isinstance(data, dict):
            raise DataValidationException(
                "数据必须是字典类型",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        for field_name, field_rules in schema.items():
            # 检查必填字段
            if field_rules.get('required', True) and field_name not in data:
                raise DataValidationException(
                    f"缺少必填字段: {field_name}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
            
            # 验证字段值
            if field_name in data:
                cls.validate(data[field_name], field_rules, field_name)
        
        return True
    
    @classmethod
    def validate_user_data(cls, uid: int, pid_list: List[str]) -> bool:
        """
        验证用户数据
        
        Args:
            uid: 用户ID
            pid_list: PID列表
            
        Returns:
            验证结果
        """
        # 验证UID
        cls.validate(uid, 'uid', 'uid')
        
        # 验证PID列表
        cls.validate(pid_list, 'list', 'pid_list')
        
        # 验证PID列表长度
        if len(pid_list) > 300:
            raise DataValidationException(
                f"PID列表长度 {len(pid_list)} 超过最大限制 300",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        # 验证每个PID
        for i, pid in enumerate(pid_list):
            cls.validate(pid, 'pid', f'pid_list[{i}]')
        
        return True
    
    @classmethod
    def validate_vector_data(cls, vector: Union[List[float], np.ndarray], 
                           expected_dim: int = 512) -> bool:
        """
        验证向量数据
        
        Args:
            vector: 向量数据
            expected_dim: 期望的向量维度
            
        Returns:
            验证结果
        """
        # 验证向量类型
        if not isinstance(vector, (list, np.ndarray)):
            raise DataValidationException(
                "向量必须是列表或numpy数组",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        # 验证向量维度
        if len(vector) != expected_dim:
            raise DataValidationException(
                f"向量维度 {len(vector)} 不匹配期望维度 {expected_dim}",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        # 验证向量元素
        for i, value in enumerate(vector):
            if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                raise DataValidationException(
                    f"向量第 {i} 个元素无效: {value}",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
        
        return True
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any], config_name: str) -> bool:
        """
        验证配置数据
        
        Args:
            config: 配置数据
            config_name: 配置名称
            
        Returns:
            验证结果
        """
        # 根据配置名称选择验证模式
        if config_name == "mongodb":
            return cls._validate_mongodb_config(config)
        elif config_name == "milvus":
            return cls._validate_milvus_config(config)
        elif config_name == "logging":
            return cls._validate_logging_config(config)
        else:
            # 通用配置验证
            return cls.validate(config, 'dict', config_name)
    
    @classmethod
    def _validate_mongodb_config(cls, config: Dict[str, Any]) -> bool:
        """验证MongoDB配置"""
        schema = {
            'connection': {
                'type': 'dict',
                'required': True
            },
            'database': {
                'type': 'non_empty_string',
                'required': True
            }
        }
        return cls.validate_dict(config, schema)
    
    @classmethod
    def _validate_milvus_config(cls, config: Dict[str, Any]) -> bool:
        """验证Milvus配置"""
        schema = {
            'connection': {
                'type': 'dict',
                'required': True
            },
            'collections': {
                'type': 'dict',
                'required': True
            }
        }
        return cls.validate_dict(config, schema)
    
    @classmethod
    def _validate_logging_config(cls, config: Dict[str, Any]) -> bool:
        """验证日志配置"""
        schema = {
            'level': {
                'type': 'string',
                'choices': ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                'required': True
            },
            'format': {
                'type': 'non_empty_string',
                'required': False
            }
        }
        return cls.validate_dict(config, schema)
    
    @classmethod
    def add_custom_rule(cls, name: str, validator: Callable[[Any], bool], 
                       error_message: str, required: bool = True):
        """
        添加自定义验证规则
        
        Args:
            name: 规则名称
            validator: 验证函数
            error_message: 错误消息
            required: 是否必填
        """
        cls.VALIDATION_RULES[name] = ValidationRule(
            name=name,
            validator=validator,
            error_message=error_message,
            required=required
        )
