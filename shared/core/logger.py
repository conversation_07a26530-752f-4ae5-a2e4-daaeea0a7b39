#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志管理器

提供项目的统一日志管理功能：
- 支持多种日志处理器（文件、控制台、远程）
- 支持日志轮转和大小控制
- 支持结构化日志
- 支持彩色日志输出
- 支持性能监控和指标收集

作者: User-DF Team
版本: 2.0.0
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
import json
import threading
from dataclasses import dataclass
import coloredlogs

from .config_manager import ConfigManager


@dataclass
class LogConfig:
    """日志配置数据类"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    file_path: Optional[str] = None
    file_max_size: str = "100MB"
    file_backup_count: int = 10
    console_enabled: bool = True
    console_colored: bool = True
    structured: bool = False


class SizeControlledRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """
    大小控制的日志文件处理器
    当日志文件超过最大大小时，删除前10%的内容而不是轮转文件
    """
    
    def __init__(self, filename, mode='a', maxBytes=0, encoding=None, delay=False):
        """
        初始化处理器
        
        Args:
            filename: 日志文件名
            mode: 文件打开模式
            maxBytes: 最大字节数
            encoding: 文件编码
            delay: 是否延迟打开文件
        """
        # 不使用backupCount，因为我们不轮转文件
        super().__init__(filename, mode, maxBytes, backupCount=0, encoding=encoding, delay=delay)
    
    def doRollover(self):
        """
        执行日志轮转
        删除文件前10%的内容而不是创建新文件
        """
        if self.stream:
            self.stream.close()
            self.stream = None
        
        try:
            # 读取当前文件内容
            with open(self.baseFilename, 'r', encoding=self.encoding or 'utf-8') as f:
                lines = f.readlines()
            
            # 计算要删除的行数（前10%）
            total_lines = len(lines)
            lines_to_remove = max(1, total_lines // 10)
            
            # 保留后90%的内容
            remaining_lines = lines[lines_to_remove:]
            
            # 写回文件
            with open(self.baseFilename, 'w', encoding=self.encoding or 'utf-8') as f:
                f.writelines(remaining_lines)
            
            # 添加轮转标记
            with open(self.baseFilename, 'a', encoding=self.encoding or 'utf-8') as f:
                f.write(f"\n--- Log rotated at {datetime.now().isoformat()} ---\n")
                
        except Exception as e:
            # 如果处理失败，创建新文件
            with open(self.baseFilename, 'w', encoding=self.encoding or 'utf-8') as f:
                f.write(f"--- Log rotation error: {e} ---\n")
        
        # 重新打开文件流
        if not self.delay:
            self.stream = self._open()


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON格式"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_data.update(record.extra_fields)
        
        return json.dumps(log_data, ensure_ascii=False)


class Logger:
    """统一日志管理器"""

    _loggers: Dict[str, logging.Logger] = {}
    _lock = threading.Lock()
    _config_manager: Optional[ConfigManager] = None
    _session_timestamp: Optional[str] = None  # 会话级别的时间戳，避免多个文件
    
    @classmethod
    def get_logger(cls, name: str, config: Optional[Dict[str, Any]] = None) -> logging.Logger:
        """
        获取日志记录器
        
        Args:
            name: 日志记录器名称
            config: 日志配置，如果不提供则使用默认配置
            
        Returns:
            配置好的日志记录器
        """
        with cls._lock:
            if name in cls._loggers:
                return cls._loggers[name]
            
            # 创建新的日志记录器
            logger = cls._create_logger(name, config)
            cls._loggers[name] = logger
            return logger
    
    @classmethod
    def _create_logger(cls, name: str, config: Optional[Dict[str, Any]] = None) -> logging.Logger:
        """创建日志记录器"""
        # 获取配置
        if config is None:
            config = cls._get_default_config(name)
        
        log_config = LogConfig(**config)
        
        # 创建日志记录器
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_config.level.upper()))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加文件处理器
        if log_config.file_enabled:
            file_handler = cls._create_file_handler(name, log_config)
            logger.addHandler(file_handler)
        
        # 添加控制台处理器
        if log_config.console_enabled:
            console_handler = cls._create_console_handler(log_config)
            logger.addHandler(console_handler)
        
        # 防止日志传播到根日志记录器
        logger.propagate = False
        
        return logger
    
    @classmethod
    def _get_default_config(cls, name: str) -> Dict[str, Any]:
        """获取默认日志配置"""
        if cls._config_manager is None:
            cls._config_manager = ConfigManager()

        try:
            # 尝试从配置文件加载
            logging_config = cls._config_manager.get_config("logging", default={})

            # 检查是否有特定日志记录器的配置
            if "loggers" in logging_config and name in logging_config["loggers"]:
                specific_config = logging_config["loggers"][name].copy()
                # 合并全局配置
                for key, value in logging_config.items():
                    if key != "loggers" and key not in specific_config:
                        specific_config[key] = value
                config = specific_config
            else:
                config = logging_config

            # 映射配置字段名称以匹配LogConfig参数
            config = cls._map_config_fields(config)
            return config

        except Exception:
            # 使用默认配置
            return {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "file_path": f"logs/{name}.log",
                "file_max_size": "100MB",
                "file_backup_count": 10,
                "console_enabled": True,
                "console_colored": True,
                "structured": False
            }

    @classmethod
    def _map_config_fields(cls, config: Dict[str, Any]) -> Dict[str, Any]:
        """映射配置字段名称以匹配LogConfig参数"""
        mapped_config = {}

        # LogConfig支持的字段
        supported_fields = {
            'level', 'format', 'file_enabled', 'file_path', 'file_max_size',
            'file_backup_count', 'console_enabled', 'console_colored', 'structured'
        }

        # 直接映射支持的字段
        for field in supported_fields:
            if field in config:
                mapped_config[field] = config[field]

        # 映射字段名称
        field_mappings = {
            'console_output': 'console_enabled',
            'enable_colors': 'console_colored',
        }

        for old_key, new_key in field_mappings.items():
            if old_key in config:
                mapped_config[new_key] = config[old_key]

        # 处理文件处理器配置
        if 'file_handler' in config:
            file_config = config['file_handler']
            if 'enabled' in file_config:
                mapped_config['file_enabled'] = file_config['enabled']
            if 'max_file_size' in file_config:
                mapped_config['file_max_size'] = file_config['max_file_size']
            if 'backup_count' in file_config:
                mapped_config['file_backup_count'] = file_config['backup_count']

        # 处理控制台处理器配置
        if 'console_handler' in config:
            console_config = config['console_handler']
            if 'enabled' in console_config:
                mapped_config['console_enabled'] = console_config['enabled']
            if 'colored' in console_config:
                mapped_config['console_colored'] = console_config['colored']

        # 处理结构化日志配置
        if 'structured_logging' in config:
            structured_config = config['structured_logging']
            if 'enabled' in structured_config:
                mapped_config['structured'] = structured_config['enabled']

        return mapped_config
    
    @classmethod
    def _create_file_handler(cls, name: str, config: LogConfig) -> logging.Handler:
        """创建文件处理器"""
        # 确定日志文件路径
        if config.file_path:
            log_file = Path(config.file_path)
        else:
            # 对于orc_mongodb_service相关的组件，统一使用orc_mongodb_service.log
            if any(component in name for component in ['ORCMongoDBService', 'ORCDataProcessor', 'MongoDBPool', 'SimpleORCReader']):
                log_file = Path("logs") / "orc_mongodb_service.log"
            else:
                log_file = Path("logs") / f"{name}.log"

        # 为日志文件添加时间戳后缀
        log_file = cls._add_timestamp_to_filename(log_file)

        # 创建日志目录
        log_file.parent.mkdir(parents=True, exist_ok=True)

        # 解析文件大小
        max_bytes = cls._parse_size(config.file_max_size)

        # 创建文件处理器
        handler = SizeControlledRotatingFileHandler(
            filename=str(log_file),
            maxBytes=max_bytes,
            encoding='utf-8'
        )

        # 设置格式化器
        if config.structured:
            formatter = StructuredFormatter()
        else:
            formatter = logging.Formatter(config.format)

        handler.setFormatter(formatter)
        return handler
    
    @classmethod
    def _create_console_handler(cls, config: LogConfig) -> logging.Handler:
        """创建控制台处理器"""
        handler = logging.StreamHandler(sys.stdout)

        # 设置格式化器
        if config.structured:
            formatter = StructuredFormatter()
        elif config.console_colored:
            # 使用coloredlogs的ColoredFormatter
            formatter = coloredlogs.ColoredFormatter(
                fmt=config.format,
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        else:
            formatter = logging.Formatter(config.format)

        handler.setFormatter(formatter)
        return handler
    
    @classmethod
    def _add_timestamp_to_filename(cls, log_file: Path) -> Path:
        """
        为日志文件名添加时间戳后缀

        Args:
            log_file: 原始日志文件路径

        Returns:
            带时间戳后缀的日志文件路径
        """
        # 使用会话级别的时间戳，确保同一次运行使用相同的时间戳
        if cls._session_timestamp is None:
            cls._session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 分离文件名和扩展名
        stem = log_file.stem
        suffix = log_file.suffix

        # 构造新的文件名：原文件名_时间戳.扩展名
        new_filename = f"{stem}_{cls._session_timestamp}{suffix}"

        return log_file.parent / new_filename

    @classmethod
    def _parse_size(cls, size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.upper().strip()

        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    @classmethod
    def reload_all_loggers(cls):
        """重新加载所有日志记录器"""
        with cls._lock:
            logger_names = list(cls._loggers.keys())
            cls._loggers.clear()
            
            for name in logger_names:
                cls.get_logger(name)
    
    @classmethod
    def set_level(cls, name: str, level: str):
        """设置日志记录器级别"""
        if name in cls._loggers:
            cls._loggers[name].setLevel(getattr(logging, level.upper()))
    
    @classmethod
    def add_extra_field(cls, logger: logging.Logger, **kwargs):
        """为日志记录器添加额外字段"""
        # 创建适配器来添加额外字段
        class ExtraAdapter(logging.LoggerAdapter):
            def process(self, msg, kwargs):
                return msg, kwargs
        
        return ExtraAdapter(logger, kwargs)
