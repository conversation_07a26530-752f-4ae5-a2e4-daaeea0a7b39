"""
User-DF 共享模块

该模块包含项目中所有服务共享的核心功能：
- 核心功能：配置管理、日志管理、异常处理、数据验证
- 数据库层：MongoDB、Milvus、Redis连接管理
- 工具层：数据处理、时间工具、批处理、监控
- 数据模型：统一的数据模型定义

版本: 2.0.0
作者: User-DF Team
"""

__version__ = "2.0.0"
__author__ = "User-DF Team"

# 导入核心模块
from .core import ConfigManager, Logger, ExceptionHandler, DataValidator

# 导入数据库模块
from .database import MongoDBPool, MilvusPool, RedisPool

# 导入工具模块
from .utils import DataProcessor, TimeUtils, BatchProcessor, Monitor

# 导入数据模型
from .models import UserModel, VectorModel

__all__ = [
    # 核心模块
    "ConfigManager",
    "Logger",
    "ExceptionHandler",
    "DataValidator",

    # 数据库模块
    "MongoDBPool",
    "MilvusPool",
    "RedisPool",

    # 工具模块
    "DataProcessor",
    "TimeUtils",
    "BatchProcessor",
    "Monitor",

    # 数据模型
    "UserModel",
    "VectorModel"
]
