#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间工具

提供时间处理的通用功能：
- 时间格式转换
- 时间戳处理
- 日期计算
- 时区处理

作者: User-DF Team
版本: 2.0.0
"""

import re
from typing import Union, Optional, List
from datetime import datetime, date, timedelta, timezone
import time
import pytz

from ..core import Logger, DataValidationException, ErrorCode


class TimeUtils:
    """时间工具类"""
    
    # Unix纪元
    UNIX_EPOCH = datetime(1970, 1, 1, tzinfo=timezone.utc)
    
    # 常用时间格式
    FORMATS = {
        'iso': '%Y-%m-%dT%H:%M:%S',
        'iso_with_tz': '%Y-%m-%dT%H:%M:%S%z',
        'date': '%Y-%m-%d',
        'datetime': '%Y-%m-%d %H:%M:%S',
        'compact_date': '%Y%m%d',
        'compact_datetime': '%Y%m%d%H%M%S',
        'timestamp': '%Y-%m-%d %H:%M:%S.%f'
    }
    
    @classmethod
    def get_logger(cls):
        """获取日志记录器"""
        if not hasattr(cls, '_logger'):
            cls._logger = Logger.get_logger("TimeUtils")
        return cls._logger
    
    @classmethod
    def now(cls, tz: Optional[Union[str, timezone]] = None) -> datetime:
        """
        获取当前时间
        
        Args:
            tz: 时区，可以是字符串或timezone对象
            
        Returns:
            当前时间
        """
        if tz is None:
            return datetime.now()
        elif isinstance(tz, str):
            timezone_obj = pytz.timezone(tz)
            return datetime.now(timezone_obj)
        else:
            return datetime.now(tz)
    
    @classmethod
    def today(cls) -> date:
        """
        获取今天的日期
        
        Returns:
            今天的日期
        """
        return date.today()
    
    @classmethod
    def to_timestamp(cls, dt: Union[datetime, date, str]) -> float:
        """
        转换为Unix时间戳
        
        Args:
            dt: 日期时间对象、日期对象或字符串
            
        Returns:
            Unix时间戳
        """
        try:
            if isinstance(dt, str):
                dt = cls.parse_datetime(dt)
            elif isinstance(dt, date) and not isinstance(dt, datetime):
                dt = datetime.combine(dt, datetime.min.time())
            
            if isinstance(dt, datetime):
                if dt.tzinfo is None:
                    dt = dt.replace(tzinfo=timezone.utc)
                return dt.timestamp()
            
            raise ValueError(f"不支持的时间类型: {type(dt)}")
            
        except Exception as e:
            cls.get_logger().error(f"转换时间戳失败: {dt}, {e}")
            raise DataValidationException(
                f"转换时间戳失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def from_timestamp(cls, timestamp: float, 
                      tz: Optional[Union[str, timezone]] = None) -> datetime:
        """
        从Unix时间戳转换为datetime
        
        Args:
            timestamp: Unix时间戳
            tz: 目标时区
            
        Returns:
            datetime对象
        """
        try:
            dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            
            if tz is not None:
                if isinstance(tz, str):
                    target_tz = pytz.timezone(tz)
                else:
                    target_tz = tz
                dt = dt.astimezone(target_tz)
            
            return dt
            
        except Exception as e:
            cls.get_logger().error(f"从时间戳转换失败: {timestamp}, {e}")
            raise DataValidationException(
                f"从时间戳转换失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def date_to_days(cls, date_value: Union[str, date, datetime]) -> int:
        """
        将日期转换为天数（自Unix纪元以来的天数）
        
        Args:
            date_value: 日期值
            
        Returns:
            天数
        """
        try:
            if isinstance(date_value, str):
                # 尝试解析不同格式的日期字符串
                if re.match(r'^\d{8}$', date_value):
                    # YYYYMMDD格式
                    dt = datetime.strptime(date_value, '%Y%m%d')
                elif re.match(r'^\d{4}-\d{2}-\d{2}$', date_value):
                    # YYYY-MM-DD格式
                    dt = datetime.strptime(date_value, '%Y-%m-%d')
                else:
                    # 尝试自动解析
                    dt = cls.parse_datetime(date_value)
            elif isinstance(date_value, date):
                dt = datetime.combine(date_value, datetime.min.time())
            elif isinstance(date_value, datetime):
                dt = date_value
            else:
                raise ValueError(f"不支持的日期格式: {type(date_value)}")
            
            # 计算自Unix纪元以来的天数
            epoch_date = cls.UNIX_EPOCH.replace(tzinfo=None)
            delta = dt.replace(tzinfo=None) - epoch_date
            return delta.days
            
        except Exception as e:
            cls.get_logger().error(f"转换日期为天数失败: {date_value}, {e}")
            raise DataValidationException(
                f"转换日期为天数失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def days_to_date(cls, days: int) -> date:
        """
        将天数转换为日期
        
        Args:
            days: 自Unix纪元以来的天数
            
        Returns:
            日期对象
        """
        try:
            epoch_date = cls.UNIX_EPOCH.date()
            target_date = epoch_date + timedelta(days=days)
            return target_date
            
        except Exception as e:
            cls.get_logger().error(f"转换天数为日期失败: {days}, {e}")
            raise DataValidationException(
                f"转换天数为日期失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def parse_datetime(cls, datetime_str: str, 
                      format_hint: Optional[str] = None) -> datetime:
        """
        解析日期时间字符串
        
        Args:
            datetime_str: 日期时间字符串
            format_hint: 格式提示
            
        Returns:
            datetime对象
        """
        try:
            # 如果提供了格式提示，优先使用
            if format_hint and format_hint in cls.FORMATS:
                return datetime.strptime(datetime_str, cls.FORMATS[format_hint])
            
            # 尝试常用格式
            for format_name, format_str in cls.FORMATS.items():
                try:
                    return datetime.strptime(datetime_str, format_str)
                except ValueError:
                    continue
            
            # 尝试ISO格式解析
            try:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            except ValueError:
                pass
            
            raise ValueError(f"无法解析日期时间字符串: {datetime_str}")
            
        except Exception as e:
            cls.get_logger().error(f"解析日期时间失败: {datetime_str}, {e}")
            raise DataValidationException(
                f"解析日期时间失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def format_datetime(cls, dt: datetime, 
                       format_name: str = 'datetime') -> str:
        """
        格式化日期时间
        
        Args:
            dt: datetime对象
            format_name: 格式名称
            
        Returns:
            格式化后的字符串
        """
        try:
            if format_name in cls.FORMATS:
                return dt.strftime(cls.FORMATS[format_name])
            else:
                # 直接使用提供的格式字符串
                return dt.strftime(format_name)
                
        except Exception as e:
            cls.get_logger().error(f"格式化日期时间失败: {dt}, {format_name}, {e}")
            raise DataValidationException(
                f"格式化日期时间失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def get_date_range(cls, start_date: Union[str, date], 
                      end_date: Union[str, date]) -> List[date]:
        """
        获取日期范围内的所有日期
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            日期列表
        """
        try:
            # 转换为date对象
            if isinstance(start_date, str):
                start_date = cls.parse_datetime(start_date).date()
            elif isinstance(start_date, datetime):
                start_date = start_date.date()
            
            if isinstance(end_date, str):
                end_date = cls.parse_datetime(end_date).date()
            elif isinstance(end_date, datetime):
                end_date = end_date.date()
            
            # 生成日期范围
            date_list = []
            current_date = start_date
            
            while current_date <= end_date:
                date_list.append(current_date)
                current_date += timedelta(days=1)
            
            return date_list
            
        except Exception as e:
            cls.get_logger().error(f"获取日期范围失败: {start_date} - {end_date}, {e}")
            raise DataValidationException(
                f"获取日期范围失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def add_days(cls, base_date: Union[str, date, datetime], 
                days: int) -> date:
        """
        日期加天数
        
        Args:
            base_date: 基础日期
            days: 要添加的天数
            
        Returns:
            新日期
        """
        try:
            if isinstance(base_date, str):
                base_date = cls.parse_datetime(base_date).date()
            elif isinstance(base_date, datetime):
                base_date = base_date.date()
            
            return base_date + timedelta(days=days)
            
        except Exception as e:
            cls.get_logger().error(f"日期加天数失败: {base_date} + {days}, {e}")
            raise DataValidationException(
                f"日期加天数失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def subtract_days(cls, base_date: Union[str, date, datetime], 
                     days: int) -> date:
        """
        日期减天数
        
        Args:
            base_date: 基础日期
            days: 要减去的天数
            
        Returns:
            新日期
        """
        return cls.add_days(base_date, -days)
    
    @classmethod
    def days_between(cls, date1: Union[str, date, datetime], 
                    date2: Union[str, date, datetime]) -> int:
        """
        计算两个日期之间的天数差
        
        Args:
            date1: 日期1
            date2: 日期2
            
        Returns:
            天数差（date2 - date1）
        """
        try:
            # 转换为date对象
            if isinstance(date1, str):
                date1 = cls.parse_datetime(date1).date()
            elif isinstance(date1, datetime):
                date1 = date1.date()
            
            if isinstance(date2, str):
                date2 = cls.parse_datetime(date2).date()
            elif isinstance(date2, datetime):
                date2 = date2.date()
            
            delta = date2 - date1
            return delta.days
            
        except Exception as e:
            cls.get_logger().error(f"计算日期差失败: {date1} - {date2}, {e}")
            raise DataValidationException(
                f"计算日期差失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
    
    @classmethod
    def is_valid_date_string(cls, date_str: str, 
                           format_name: Optional[str] = None) -> bool:
        """
        验证日期字符串是否有效
        
        Args:
            date_str: 日期字符串
            format_name: 格式名称
            
        Returns:
            是否有效
        """
        try:
            cls.parse_datetime(date_str, format_name)
            return True
        except:
            return False
    
    @classmethod
    def get_current_partition_date(cls, format_name: str = 'compact_date') -> str:
        """
        获取当前分区日期
        
        Args:
            format_name: 格式名称
            
        Returns:
            格式化的日期字符串
        """
        return cls.format_datetime(cls.now(), format_name)
    
    @classmethod
    def convert_timezone(cls, dt: datetime, 
                        from_tz: Union[str, timezone],
                        to_tz: Union[str, timezone]) -> datetime:
        """
        转换时区
        
        Args:
            dt: datetime对象
            from_tz: 源时区
            to_tz: 目标时区
            
        Returns:
            转换后的datetime对象
        """
        try:
            # 转换时区对象
            if isinstance(from_tz, str):
                from_tz = pytz.timezone(from_tz)
            if isinstance(to_tz, str):
                to_tz = pytz.timezone(to_tz)
            
            # 如果datetime没有时区信息，先设置源时区
            if dt.tzinfo is None:
                dt = from_tz.localize(dt)
            
            # 转换到目标时区
            return dt.astimezone(to_tz)
            
        except Exception as e:
            cls.get_logger().error(f"时区转换失败: {dt}, {from_tz} -> {to_tz}, {e}")
            raise DataValidationException(
                f"时区转换失败: {e}",
                ErrorCode.DATA_CONVERSION_ERROR
            )
