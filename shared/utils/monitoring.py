#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控工具

提供系统监控功能：
- 性能监控
- 资源使用监控
- 健康检查
- 指标收集

作者: User-DF Team
版本: 2.0.0
"""

import time
import threading
import psutil
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime

from ..core import Logger


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float = field(default_factory=time.time)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    memory_available_mb: float = 0.0
    disk_usage_percent: float = 0.0
    network_bytes_sent: int = 0
    network_bytes_recv: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp,
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_used_mb": self.memory_used_mb,
            "memory_available_mb": self.memory_available_mb,
            "disk_usage_percent": self.disk_usage_percent,
            "network_bytes_sent": self.network_bytes_sent,
            "network_bytes_recv": self.network_bytes_recv
        }


@dataclass
class ProcessMetrics:
    """进程指标"""
    timestamp: float = field(default_factory=time.time)
    pid: int = 0
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_rss_mb: float = 0.0
    memory_vms_mb: float = 0.0
    num_threads: int = 0
    num_fds: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp,
            "pid": self.pid,
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_rss_mb": self.memory_rss_mb,
            "memory_vms_mb": self.memory_vms_mb,
            "num_threads": self.num_threads,
            "num_fds": self.num_fds
        }


class Monitor:
    """监控器"""
    
    def __init__(self, interval: float = 60.0):
        """
        初始化监控器
        
        Args:
            interval: 监控间隔（秒）
        """
        self.interval = interval
        self.logger = Logger.get_logger("Monitor")
        
        # 监控状态
        self._running = False
        self._thread = None
        self._lock = threading.Lock()
        
        # 指标存储
        self._system_metrics: List[SystemMetrics] = []
        self._process_metrics: List[ProcessMetrics] = []
        self._max_metrics = 1000  # 最大保存指标数量
        
        # 回调函数
        self._callbacks: List[Callable[[Dict[str, Any]], None]] = []
        
        # 进程对象
        self._process = psutil.Process()
    
    def start(self):
        """开始监控"""
        with self._lock:
            if self._running:
                self.logger.warning("监控器已经在运行")
                return
            
            self._running = True
            self._thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._thread.start()
            
            self.logger.info(f"监控器已启动，间隔: {self.interval}s")
    
    def stop(self):
        """停止监控"""
        with self._lock:
            if not self._running:
                return
            
            self._running = False
            
            if self._thread and self._thread.is_alive():
                self._thread.join(timeout=5.0)
            
            self.logger.info("监控器已停止")
    
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加监控回调"""
        self._callbacks.append(callback)
    
    def get_system_metrics(self, count: Optional[int] = None) -> List[SystemMetrics]:
        """获取系统指标"""
        with self._lock:
            if count is None:
                return self._system_metrics.copy()
            else:
                return self._system_metrics[-count:].copy()
    
    def get_process_metrics(self, count: Optional[int] = None) -> List[ProcessMetrics]:
        """获取进程指标"""
        with self._lock:
            if count is None:
                return self._process_metrics.copy()
            else:
                return self._process_metrics[-count:].copy()
    
    def get_latest_metrics(self) -> Dict[str, Any]:
        """获取最新指标"""
        with self._lock:
            result = {}
            
            if self._system_metrics:
                result["system"] = self._system_metrics[-1].to_dict()
            
            if self._process_metrics:
                result["process"] = self._process_metrics[-1].to_dict()
            
            return result
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络使用情况
            network = psutil.net_io_counters()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                memory_available_mb=memory.available / (1024 * 1024),
                disk_usage_percent=disk.percent,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics()
    
    def collect_process_metrics(self) -> ProcessMetrics:
        """收集进程指标"""
        try:
            # 进程信息
            memory_info = self._process.memory_info()
            
            metrics = ProcessMetrics(
                pid=self._process.pid,
                cpu_percent=self._process.cpu_percent(),
                memory_percent=self._process.memory_percent(),
                memory_rss_mb=memory_info.rss / (1024 * 1024),
                memory_vms_mb=memory_info.vms / (1024 * 1024),
                num_threads=self._process.num_threads()
            )
            
            # 文件描述符数量（仅Unix系统）
            try:
                metrics.num_fds = self._process.num_fds()
            except (AttributeError, psutil.AccessDenied):
                metrics.num_fds = 0
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集进程指标失败: {e}")
            return ProcessMetrics()
    
    def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                # 收集指标
                system_metrics = self.collect_system_metrics()
                process_metrics = self.collect_process_metrics()
                
                # 存储指标
                with self._lock:
                    self._system_metrics.append(system_metrics)
                    self._process_metrics.append(process_metrics)
                    
                    # 限制指标数量
                    if len(self._system_metrics) > self._max_metrics:
                        self._system_metrics = self._system_metrics[-self._max_metrics:]
                    
                    if len(self._process_metrics) > self._max_metrics:
                        self._process_metrics = self._process_metrics[-self._max_metrics:]
                
                # 调用回调函数
                metrics_data = {
                    "system": system_metrics.to_dict(),
                    "process": process_metrics.to_dict()
                }
                
                for callback in self._callbacks:
                    try:
                        callback(metrics_data)
                    except Exception as e:
                        self.logger.error(f"监控回调失败: {e}")
                
                # 等待下次监控
                time.sleep(self.interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(self.interval)
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            latest_metrics = self.get_latest_metrics()
            
            # 健康状态判断
            health_status = "healthy"
            issues = []
            
            if "system" in latest_metrics:
                system = latest_metrics["system"]
                
                # CPU使用率检查
                if system["cpu_percent"] > 90:
                    health_status = "warning"
                    issues.append(f"CPU使用率过高: {system['cpu_percent']:.1f}%")
                
                # 内存使用率检查
                if system["memory_percent"] > 90:
                    health_status = "critical"
                    issues.append(f"内存使用率过高: {system['memory_percent']:.1f}%")
                
                # 磁盘使用率检查
                if system["disk_usage_percent"] > 90:
                    health_status = "warning"
                    issues.append(f"磁盘使用率过高: {system['disk_usage_percent']:.1f}%")
            
            if "process" in latest_metrics:
                process = latest_metrics["process"]
                
                # 进程内存使用检查
                if process["memory_percent"] > 50:
                    health_status = "warning"
                    issues.append(f"进程内存使用率过高: {process['memory_percent']:.1f}%")
            
            return {
                "status": health_status,
                "timestamp": time.time(),
                "issues": issues,
                "metrics": latest_metrics
            }
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "timestamp": time.time(),
                "error": str(e)
            }
    
    def get_summary(self) -> Dict[str, Any]:
        """获取监控摘要"""
        try:
            system_metrics = self.get_system_metrics(count=10)
            process_metrics = self.get_process_metrics(count=10)
            
            if not system_metrics or not process_metrics:
                return {"error": "没有足够的监控数据"}
            
            # 计算平均值
            avg_cpu = sum(m.cpu_percent for m in system_metrics) / len(system_metrics)
            avg_memory = sum(m.memory_percent for m in system_metrics) / len(system_metrics)
            avg_process_cpu = sum(m.cpu_percent for m in process_metrics) / len(process_metrics)
            avg_process_memory = sum(m.memory_percent for m in process_metrics) / len(process_metrics)
            
            return {
                "monitoring_duration": time.time() - system_metrics[0].timestamp,
                "data_points": len(system_metrics),
                "average_cpu_percent": avg_cpu,
                "average_memory_percent": avg_memory,
                "average_process_cpu_percent": avg_process_cpu,
                "average_process_memory_percent": avg_process_memory,
                "latest_metrics": self.get_latest_metrics()
            }
            
        except Exception as e:
            self.logger.error(f"获取监控摘要失败: {e}")
            return {"error": str(e)}
