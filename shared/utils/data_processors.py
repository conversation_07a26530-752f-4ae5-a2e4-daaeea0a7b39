#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理工具

提供通用的数据处理功能：
- 数据清洗和转换
- 数据验证和格式化
- 批量数据处理
- 数据去重和聚合

作者: User-DF Team
版本: 2.0.0
"""

import re
from typing import Dict, List, Any, Optional, Union, Callable, Tuple, Set
from dataclasses import dataclass
from datetime import datetime, date
import numpy as np
import pandas as pd
from collections import defaultdict, Counter

from ..core import Logger, DataValidator, DataValidationException, ErrorCode


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    processed_count: int = 0
    error_count: int = 0
    skipped_count: int = 0
    execution_time: float = 0.0
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class DataProcessor:
    """数据处理工具"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = Logger.get_logger("DataProcessor")
        
        # 处理统计
        self.stats = {
            "total_processed": 0,
            "total_errors": 0,
            "total_skipped": 0
        }
    
    def clean_text(self, text: str, 
                   remove_special_chars: bool = True,
                   normalize_whitespace: bool = True,
                   to_lowercase: bool = False) -> str:
        """
        清洗文本数据
        
        Args:
            text: 输入文本
            remove_special_chars: 是否移除特殊字符
            normalize_whitespace: 是否规范化空白字符
            to_lowercase: 是否转换为小写
            
        Returns:
            清洗后的文本
        """
        if not isinstance(text, str):
            return str(text) if text is not None else ""
        
        result = text
        
        # 移除特殊字符
        if remove_special_chars:
            result = re.sub(r'[^\w\s\u4e00-\u9fff]', '', result)
        
        # 规范化空白字符
        if normalize_whitespace:
            result = re.sub(r'\s+', ' ', result).strip()
        
        # 转换为小写
        if to_lowercase:
            result = result.lower()
        
        return result
    
    def validate_and_convert_uid(self, uid: Any) -> Optional[int]:
        """
        验证和转换UID
        
        Args:
            uid: 输入UID
            
        Returns:
            转换后的UID或None
        """
        try:
            # 尝试转换为整数
            if isinstance(uid, str):
                uid_int = int(uid.strip())
            elif isinstance(uid, (int, float)):
                uid_int = int(uid)
            else:
                return None
            
            # 验证UID范围
            DataValidator.validate(uid_int, 'uid', 'uid')
            return uid_int
            
        except (ValueError, DataValidationException):
            return None
    
    def validate_and_convert_pid(self, pid: Any) -> Optional[str]:
        """
        验证和转换PID
        
        Args:
            pid: 输入PID
            
        Returns:
            转换后的PID或None
        """
        try:
            if pid is None:
                return None
            
            # 处理不同类型的PID输入
            if isinstance(pid, str):
                pid_str = pid.strip()
                
                # 处理字符串形式的列表，如 "['pid1']"
                if pid_str.startswith('[') and pid_str.endswith(']'):
                    # 移除方括号
                    inner_content = pid_str[1:-1].strip()
                    # 移除引号
                    if (inner_content.startswith("'") and inner_content.endswith("'")) or \
                       (inner_content.startswith('"') and inner_content.endswith('"')):
                        pid_str = inner_content[1:-1]
                    else:
                        pid_str = inner_content
                # 处理普通的引号包围的字符串
                elif (pid_str.startswith("'") and pid_str.endswith("'")) or \
                     (pid_str.startswith('"') and pid_str.endswith('"')):
                    pid_str = pid_str[1:-1]
                    
            elif isinstance(pid, (int, float)):
                pid_str = str(pid).strip()
            elif isinstance(pid, (list, tuple)):
                # 处理列表或元组类型，取第一个元素
                if len(pid) > 0:
                    first_element = pid[0]
                    if isinstance(first_element, str):
                        pid_str = first_element.strip()
                        # 移除可能的引号
                        if (pid_str.startswith("'") and pid_str.endswith("'")) or \
                           (pid_str.startswith('"') and pid_str.endswith('"')):
                            pid_str = pid_str[1:-1]
                    else:
                        pid_str = str(first_element).strip()
                else:
                    return None
            else:
                # 对于其他类型，直接转换为字符串可能包含不需要的字符
                pid_str = str(pid).strip()
                # 移除列表表示的方括号和引号
                if pid_str.startswith('[') and pid_str.endswith(']'):
                    pid_str = pid_str[1:-1].strip()
                    # 移除引号
                    if pid_str.startswith("'") and pid_str.endswith("'"):
                        pid_str = pid_str[1:-1]
                    elif pid_str.startswith('"') and pid_str.endswith('"'):
                        pid_str = pid_str[1:-1]
            
            # 最终清理：移除多余的引号
            pid_str = pid_str.strip("'\"")
            
            # 验证PID格式
            if len(pid_str) == 0:
                return None
                
            DataValidator.validate(pid_str, 'pid', 'pid')
            return pid_str
            
        except DataValidationException:
            return None
    
    def process_user_data(self, uid: Any, pid_list: List[Any], 
                         max_pids: int = 300) -> Optional[Dict[str, Any]]:
        """
        处理用户数据
        
        Args:
            uid: 用户ID
            pid_list: PID列表
            max_pids: 最大PID数量
            
        Returns:
            处理后的用户数据或None
        """
        try:
            # 验证和转换UID
            processed_uid = self.validate_and_convert_uid(uid)
            if processed_uid is None:
                return None
            
            # 处理PID列表
            processed_pids = []
            for pid in pid_list:
                processed_pid = self.validate_and_convert_pid(pid)
                if processed_pid is not None:
                    processed_pids.append(processed_pid)
            
            # 去重并限制数量
            unique_pids = list(dict.fromkeys(processed_pids))  # 保持顺序的去重
            if len(unique_pids) > max_pids:
                unique_pids = unique_pids[-max_pids:]  # 保留最新的PIDs
            
            return {
                "uid": processed_uid,
                "pid_list": unique_pids,
                "pid_count": len(unique_pids)
            }
            
        except Exception as e:
            self.logger.warning(f"处理用户数据失败: uid={uid}, error={e}")
            return None
    
    def deduplicate_list(self, items: List[Any], 
                        preserve_order: bool = True) -> List[Any]:
        """
        列表去重
        
        Args:
            items: 输入列表
            preserve_order: 是否保持顺序
            
        Returns:
            去重后的列表
        """
        if not items:
            return []
        
        if preserve_order:
            # 保持顺序的去重
            seen = set()
            result = []
            for item in items:
                if item not in seen:
                    seen.add(item)
                    result.append(item)
            return result
        else:
            # 不保持顺序的去重（更快）
            return list(set(items))
    
    def group_by_timestamp(self, data: List[Dict[str, Any]], 
                          timestamp_field: str = "timestamp") -> Dict[Any, List[Dict[str, Any]]]:
        """
        按时间戳分组数据
        
        Args:
            data: 输入数据列表
            timestamp_field: 时间戳字段名
            
        Returns:
            分组后的数据
        """
        grouped = defaultdict(list)
        
        for item in data:
            if timestamp_field in item:
                timestamp = item[timestamp_field]
                grouped[timestamp].append(item)
        
        return dict(grouped)
    
    def aggregate_pid_by_timestamp(self, pid_data: List[Dict[str, Any]]) -> Dict[int, List[str]]:
        """
        按时间戳聚合PID数据
        
        Args:
            pid_data: PID数据列表，格式: [{"pid": "xxx", "timestamp": 123}, ...]
            
        Returns:
            聚合后的数据，格式: {timestamp: [pid1, pid2, ...]}
        """
        aggregated = defaultdict(list)
        
        for item in pid_data:
            if "pid" in item and "timestamp" in item:
                timestamp = item["timestamp"]
                pid = item["pid"]
                
                # 验证PID
                processed_pid = self.validate_and_convert_pid(pid)
                if processed_pid is not None:
                    aggregated[timestamp].append(processed_pid)
        
        # 去重每个时间戳下的PID
        for timestamp in aggregated:
            aggregated[timestamp] = self.deduplicate_list(aggregated[timestamp])
        
        return dict(aggregated)
    
    def filter_data_by_date_range(self, data: List[Dict[str, Any]], 
                                 start_date: Union[str, date], 
                                 end_date: Union[str, date],
                                 date_field: str = "date") -> List[Dict[str, Any]]:
        """
        按日期范围过滤数据
        
        Args:
            data: 输入数据列表
            start_date: 开始日期
            end_date: 结束日期
            date_field: 日期字段名
            
        Returns:
            过滤后的数据
        """
        from .time_utils import TimeUtils
        
        # 转换日期为天数
        start_days = TimeUtils.date_to_days(start_date)
        end_days = TimeUtils.date_to_days(end_date)
        
        filtered_data = []
        for item in data:
            if date_field in item:
                item_date = item[date_field]
                
                # 转换项目日期
                if isinstance(item_date, int):
                    item_days = item_date
                else:
                    item_days = TimeUtils.date_to_days(item_date)
                
                # 检查是否在范围内
                if start_days <= item_days <= end_days:
                    filtered_data.append(item)
        
        return filtered_data
    
    def batch_process_data(self, data: List[Any], 
                          processor_func: Callable[[Any], Any],
                          batch_size: int = 1000,
                          skip_errors: bool = True) -> ProcessingResult:
        """
        批量处理数据
        
        Args:
            data: 输入数据列表
            processor_func: 处理函数
            batch_size: 批次大小
            skip_errors: 是否跳过错误
            
        Returns:
            处理结果
        """
        import time
        
        start_time = time.time()
        processed_count = 0
        error_count = 0
        skipped_count = 0
        errors = []
        
        try:
            total_items = len(data)
            num_batches = (total_items + batch_size - 1) // batch_size
            
            self.logger.info(f"开始批量处理数据: 总数={total_items}, 批次大小={batch_size}, 批次数={num_batches}")
            
            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, total_items)
                batch_data = data[start_idx:end_idx]
                
                # 处理批次数据
                for item in batch_data:
                    try:
                        result = processor_func(item)
                        if result is not None:
                            processed_count += 1
                        else:
                            skipped_count += 1
                    except Exception as e:
                        error_count += 1
                        error_msg = f"处理项目失败: {str(item)[:100]}, 错误: {e}"
                        errors.append(error_msg)
                        
                        if not skip_errors:
                            raise e
                        
                        self.logger.warning(error_msg)
                
                # 记录进度
                if (i + 1) % 10 == 0 or (i + 1) == num_batches:
                    progress = (i + 1) / num_batches * 100
                    self.logger.info(f"批量处理进度: {progress:.1f}% ({i+1}/{num_batches})")
            
            execution_time = time.time() - start_time
            
            # 更新统计
            self.stats["total_processed"] += processed_count
            self.stats["total_errors"] += error_count
            self.stats["total_skipped"] += skipped_count
            
            self.logger.info(f"批量处理完成: 处理={processed_count}, 错误={error_count}, 跳过={skipped_count}, 耗时={execution_time:.2f}秒")
            
            return ProcessingResult(
                success=True,
                processed_count=processed_count,
                error_count=error_count,
                skipped_count=skipped_count,
                execution_time=execution_time,
                details={"errors": errors[:10]}  # 只保留前10个错误
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"批量处理失败: {e}"
            self.logger.error(error_msg)
            
            return ProcessingResult(
                success=False,
                processed_count=processed_count,
                error_count=error_count,
                skipped_count=skipped_count,
                execution_time=execution_time,
                error_message=error_msg
            )
    
    def calculate_statistics(self, data: List[Union[int, float]]) -> Dict[str, float]:
        """
        计算数据统计信息
        
        Args:
            data: 数值数据列表
            
        Returns:
            统计信息字典
        """
        if not data:
            return {}
        
        try:
            data_array = np.array(data)
            
            return {
                "count": len(data),
                "mean": float(np.mean(data_array)),
                "median": float(np.median(data_array)),
                "std": float(np.std(data_array)),
                "min": float(np.min(data_array)),
                "max": float(np.max(data_array)),
                "q25": float(np.percentile(data_array, 25)),
                "q75": float(np.percentile(data_array, 75))
            }
        except Exception as e:
            self.logger.error(f"计算统计信息失败: {e}")
            return {}
    
    def normalize_vector(self, vector: List[float]) -> List[float]:
        """
        向量归一化
        
        Args:
            vector: 输入向量
            
        Returns:
            归一化后的向量
        """
        try:
            vector_array = np.array(vector)
            norm = np.linalg.norm(vector_array)
            
            if norm == 0:
                return vector
            
            normalized = vector_array / norm
            return normalized.tolist()
            
        except Exception as e:
            self.logger.error(f"向量归一化失败: {e}")
            return vector
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.stats.copy()
