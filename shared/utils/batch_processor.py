#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理框架

提供通用的批处理功能：
- 批量数据处理
- 并发处理支持
- 进度监控
- 错误处理和重试

作者: User-DF Team
版本: 2.0.0
"""

import time
import threading
from typing import List, Any, Callable, Optional, Dict, Iterator
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp

from ..core import Logger, ExceptionHandler


@dataclass
class BatchConfig:
    """批处理配置"""
    batch_size: int = 100
    max_workers: int = 4
    timeout: Optional[float] = None
    retry_count: int = 3
    retry_delay: float = 1.0
    progress_callback: Optional[Callable] = None


@dataclass
class BatchResult:
    """批处理结果"""
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    errors: List[Exception] = None
    processing_time: float = 0.0
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.processed_items == 0:
            return 0.0
        return self.successful_items / self.processed_items
    
    @property
    def items_per_second(self) -> float:
        """每秒处理项目数"""
        if self.processing_time == 0:
            return 0.0
        return self.processed_items / self.processing_time


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, config: Optional[BatchConfig] = None):
        """
        初始化批处理器
        
        Args:
            config: 批处理配置
        """
        self.config = config or BatchConfig()
        self.logger = Logger.get_logger("BatchProcessor")
        self._stats = BatchResult()
        self._lock = threading.Lock()
    
    def process_items(self, items: List[Any], 
                     processor_func: Callable[[Any], Any],
                     **kwargs) -> BatchResult:
        """
        处理项目列表
        
        Args:
            items: 要处理的项目列表
            processor_func: 处理函数
            **kwargs: 传递给处理函数的额外参数
            
        Returns:
            批处理结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始批处理: {len(items)} 个项目")
            
            # 重置统计
            self._stats = BatchResult(total_items=len(items))
            
            # 分批处理
            batches = self._create_batches(items)
            
            if self.config.max_workers > 1:
                result = self._process_batches_concurrent(batches, processor_func, **kwargs)
            else:
                result = self._process_batches_sequential(batches, processor_func, **kwargs)
            
            # 更新处理时间
            result.processing_time = time.time() - start_time
            
            self.logger.info(
                f"批处理完成: 成功 {result.successful_items}/{result.total_items}, "
                f"耗时 {result.processing_time:.2f}s, "
                f"速度 {result.items_per_second:.1f} 项/秒"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"批处理失败: {e}")
            ExceptionHandler.handle_exception(e)
            
            # 返回错误结果
            result = BatchResult(
                total_items=len(items),
                processing_time=time.time() - start_time,
                errors=[e]
            )
            return result
    
    def process_batches(self, batches: Iterator[List[Any]],
                       processor_func: Callable[[List[Any]], Any],
                       **kwargs) -> BatchResult:
        """
        处理批次迭代器
        
        Args:
            batches: 批次迭代器
            processor_func: 批次处理函数
            **kwargs: 传递给处理函数的额外参数
            
        Returns:
            批处理结果
        """
        start_time = time.time()
        result = BatchResult()
        
        try:
            for batch in batches:
                batch_result = self._process_single_batch(batch, processor_func, **kwargs)
                
                # 更新统计
                with self._lock:
                    result.total_items += len(batch)
                    result.processed_items += len(batch)
                    
                    if batch_result:
                        result.successful_items += len(batch)
                    else:
                        result.failed_items += len(batch)
                
                # 进度回调
                if self.config.progress_callback:
                    self.config.progress_callback(result)
            
            result.processing_time = time.time() - start_time
            return result
            
        except Exception as e:
            self.logger.error(f"批次处理失败: {e}")
            result.errors.append(e)
            result.processing_time = time.time() - start_time
            return result
    
    def _create_batches(self, items: List[Any]) -> List[List[Any]]:
        """创建批次"""
        batch_size = self.config.batch_size
        return [
            items[i:i + batch_size] 
            for i in range(0, len(items), batch_size)
        ]
    
    def _process_batches_sequential(self, batches: List[List[Any]],
                                  processor_func: Callable,
                                  **kwargs) -> BatchResult:
        """顺序处理批次"""
        result = BatchResult(total_items=sum(len(batch) for batch in batches))
        
        for i, batch in enumerate(batches):
            self.logger.debug(f"处理批次 {i+1}/{len(batches)}: {len(batch)} 个项目")
            
            batch_success = 0
            batch_failed = 0
            
            for item in batch:
                try:
                    processor_func(item, **kwargs)
                    batch_success += 1
                except Exception as e:
                    batch_failed += 1
                    result.errors.append(e)
                    self.logger.warning(f"项目处理失败: {e}")
            
            result.processed_items += len(batch)
            result.successful_items += batch_success
            result.failed_items += batch_failed
            
            # 进度回调
            if self.config.progress_callback:
                self.config.progress_callback(result)
        
        return result
    
    def _process_batches_concurrent(self, batches: List[List[Any]],
                                  processor_func: Callable,
                                  **kwargs) -> BatchResult:
        """并发处理批次"""
        result = BatchResult(total_items=sum(len(batch) for batch in batches))
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交任务
            future_to_batch = {
                executor.submit(self._process_batch_items, batch, processor_func, **kwargs): batch
                for batch in batches
            }
            
            # 收集结果
            for future in as_completed(future_to_batch, timeout=self.config.timeout):
                batch = future_to_batch[future]
                
                try:
                    batch_success, batch_errors = future.result()
                    
                    with self._lock:
                        result.processed_items += len(batch)
                        result.successful_items += batch_success
                        result.failed_items += len(batch) - batch_success
                        result.errors.extend(batch_errors)
                    
                    # 进度回调
                    if self.config.progress_callback:
                        self.config.progress_callback(result)
                        
                except Exception as e:
                    self.logger.error(f"批次处理异常: {e}")
                    with self._lock:
                        result.processed_items += len(batch)
                        result.failed_items += len(batch)
                        result.errors.append(e)
        
        return result
    
    def _process_batch_items(self, batch: List[Any], 
                           processor_func: Callable,
                           **kwargs) -> tuple:
        """处理批次中的项目"""
        success_count = 0
        errors = []
        
        for item in batch:
            try:
                processor_func(item, **kwargs)
                success_count += 1
            except Exception as e:
                errors.append(e)
                self.logger.warning(f"项目处理失败: {e}")
        
        return success_count, errors
    
    def _process_single_batch(self, batch: List[Any],
                            processor_func: Callable,
                            **kwargs) -> bool:
        """处理单个批次"""
        try:
            processor_func(batch, **kwargs)
            return True
        except Exception as e:
            self.logger.error(f"批次处理失败: {e}")
            return False
    
    def get_stats(self) -> BatchResult:
        """获取处理统计"""
        return self._stats
