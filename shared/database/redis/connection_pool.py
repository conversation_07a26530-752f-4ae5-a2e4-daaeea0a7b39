#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis连接池管理器

提供Redis的统一连接池管理功能：
- 连接池管理和复用
- 集群支持
- 哨兵支持
- 连接监控和统计

作者: User-DF Team
版本: 2.0.0
"""

import threading
import time
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
import redis
from redis.sentinel import Sentinel
import logging

from ...core import <PERSON><PERSON>g<PERSON>ana<PERSON>, Logger, ExceptionHandler, NetworkException, ErrorCode


@dataclass
class RedisConfig:
    """Redis配置"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    username: Optional[str] = None
    socket_timeout: float = 5.0
    socket_connect_timeout: float = 5.0
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = None
    max_connections: int = 50
    retry_on_timeout: bool = True
    health_check_interval: int = 30
    
    # 集群配置
    cluster_enabled: bool = False
    cluster_nodes: List[Dict[str, Any]] = None
    
    # 哨兵配置
    sentinel_enabled: bool = False
    sentinel_hosts: List[Tuple[str, int]] = None
    sentinel_service_name: str = "mymaster"


@dataclass
class PoolStats:
    """连接池统计信息"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_requests: int = 0
    failed_requests: int = 0


class RedisPool:
    """Redis连接池管理器"""
    
    _instances: Dict[str, 'RedisPool'] = {}
    _lock = threading.Lock()
    
    def __new__(cls, config_name: str = "redis", **kwargs):
        """单例模式，每个配置名称对应一个实例"""
        if config_name not in cls._instances:
            with cls._lock:
                if config_name not in cls._instances:
                    instance = super().__new__(cls)
                    cls._instances[config_name] = instance
        return cls._instances[config_name]
    
    def __init__(self, config_name: str = "redis", config_manager: Optional[ConfigManager] = None):
        """
        初始化Redis连接池
        
        Args:
            config_name: 配置名称
            config_manager: 配置管理器实例
        """
        if hasattr(self, '_initialized'):
            return
            
        self.config_name = config_name
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger(f"RedisPool.{config_name}")
        
        # 加载配置
        self._load_config()
        
        # 连接池
        self._pool = None
        self._sentinel = None
        self._stats = PoolStats()
        
        # 健康检查
        self._last_health_check = 0
        
        # 初始化连接池
        self._initialize_pool()
        
        self._initialized = True
        self.logger.info(f"Redis连接池初始化完成: {self.config.host}:{self.config.port}")
    
    def _load_config(self):
        """加载配置"""
        try:
            config_dict = self.config_manager.get_config(self.config_name, default={})
            
            # 基础连接配置
            connection_config = config_dict.get("connection", {})
            
            self.config = RedisConfig(
                host=connection_config.get("host", "localhost"),
                port=connection_config.get("port", 6379),
                db=connection_config.get("db", 0),
                password=connection_config.get("password"),
                username=connection_config.get("username"),
                socket_timeout=connection_config.get("socket_timeout", 5.0),
                socket_connect_timeout=connection_config.get("socket_connect_timeout", 5.0),
                socket_keepalive=connection_config.get("socket_keepalive", True),
                socket_keepalive_options=connection_config.get("socket_keepalive_options", {}),
                max_connections=connection_config.get("max_connections", 50),
                retry_on_timeout=connection_config.get("retry_on_timeout", True),
                health_check_interval=connection_config.get("health_check_interval", 30)
            )
            
            # 集群配置
            cluster_config = config_dict.get("cluster", {})
            self.config.cluster_enabled = cluster_config.get("enabled", False)
            self.config.cluster_nodes = cluster_config.get("nodes", [])
            
            # 哨兵配置
            sentinel_config = config_dict.get("sentinel", {})
            self.config.sentinel_enabled = sentinel_config.get("enabled", False)
            self.config.sentinel_hosts = [
                (host["host"], host["port"]) 
                for host in sentinel_config.get("hosts", [])
            ]
            self.config.sentinel_service_name = sentinel_config.get("service_name", "mymaster")
            
        except Exception as e:
            self.logger.error(f"加载Redis配置失败: {e}")
            # 使用默认配置
            self.config = RedisConfig()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            if self.config.sentinel_enabled and self.config.sentinel_hosts:
                # 哨兵模式
                self._initialize_sentinel_pool()
            elif self.config.cluster_enabled and self.config.cluster_nodes:
                # 集群模式
                self._initialize_cluster_pool()
            else:
                # 单机模式
                self._initialize_single_pool()
            
            # 测试连接
            self._test_connection()
            
        except Exception as e:
            self.logger.error(f"初始化Redis连接池失败: {e}")
            raise NetworkException(
                f"Redis连接池初始化失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    def _initialize_single_pool(self):
        """初始化单机连接池"""
        pool_kwargs = {
            "host": self.config.host,
            "port": self.config.port,
            "db": self.config.db,
            "password": self.config.password,
            "username": self.config.username,
            "socket_timeout": self.config.socket_timeout,
            "socket_connect_timeout": self.config.socket_connect_timeout,
            "socket_keepalive": self.config.socket_keepalive,
            "socket_keepalive_options": self.config.socket_keepalive_options,
            "max_connections": self.config.max_connections,
            "retry_on_timeout": self.config.retry_on_timeout,
            "health_check_interval": self.config.health_check_interval,
            "decode_responses": True
        }
        
        self._pool = redis.ConnectionPool(**pool_kwargs)
        self.logger.info("Redis单机连接池初始化完成")
    
    def _initialize_cluster_pool(self):
        """初始化集群连接池"""
        try:
            from rediscluster import RedisCluster
            
            startup_nodes = [
                {"host": node["host"], "port": node["port"]}
                for node in self.config.cluster_nodes
            ]
            
            self._pool = RedisCluster(
                startup_nodes=startup_nodes,
                password=self.config.password,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
                max_connections=self.config.max_connections,
                decode_responses=True,
                skip_full_coverage_check=True
            )
            
            self.logger.info("Redis集群连接池初始化完成")
            
        except ImportError:
            self.logger.error("Redis集群模式需要安装 redis-py-cluster")
            raise NetworkException(
                "Redis集群模式需要安装 redis-py-cluster",
                ErrorCode.CONFIGURATION_ERROR
            )
    
    def _initialize_sentinel_pool(self):
        """初始化哨兵连接池"""
        self._sentinel = Sentinel(
            self.config.sentinel_hosts,
            socket_timeout=self.config.socket_timeout,
            socket_connect_timeout=self.config.socket_connect_timeout,
            password=self.config.password
        )
        
        # 获取主服务器连接池
        self._pool = self._sentinel.master_for(
            self.config.sentinel_service_name,
            socket_timeout=self.config.socket_timeout,
            socket_connect_timeout=self.config.socket_connect_timeout,
            password=self.config.password,
            db=self.config.db,
            decode_responses=True
        ).connection_pool
        
        self.logger.info("Redis哨兵连接池初始化完成")
    
    def _test_connection(self):
        """测试连接"""
        try:
            client = self.get_client()
            client.ping()
            self.logger.info("Redis连接测试成功")
        except Exception as e:
            self.logger.error(f"Redis连接测试失败: {e}")
            raise
    
    def get_client(self) -> redis.Redis:
        """
        获取Redis客户端
        
        Returns:
            Redis客户端
        """
        try:
            self._stats.total_requests += 1
            
            # 健康检查
            self._check_health()
            
            if self.config.cluster_enabled:
                # 集群模式直接返回集群客户端
                client = self._pool
            else:
                # 单机或哨兵模式
                client = redis.Redis(connection_pool=self._pool)
            
            self._stats.active_connections += 1
            return client
            
        except Exception as e:
            self._stats.failed_requests += 1
            self.logger.error(f"获取Redis客户端失败: {e}")
            raise NetworkException(
                f"获取Redis客户端失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    def _check_health(self):
        """健康检查"""
        current_time = time.time()
        
        # 限制健康检查频率
        if current_time - self._last_health_check < self.config.health_check_interval:
            return
        
        self._last_health_check = current_time
        
        try:
            client = redis.Redis(connection_pool=self._pool)
            client.ping()
            self.logger.debug("Redis健康检查通过")
        except Exception as e:
            self.logger.warning(f"Redis健康检查失败: {e}")
            # 可以在这里实现重连逻辑
    
    def execute_command(self, command: str, *args, **kwargs):
        """
        执行Redis命令
        
        Args:
            command: 命令名称
            *args: 命令参数
            **kwargs: 关键字参数
            
        Returns:
            命令执行结果
        """
        try:
            client = self.get_client()
            return client.execute_command(command, *args, **kwargs)
        except Exception as e:
            self.logger.error(f"执行Redis命令失败: {command}, {e}")
            raise NetworkException(
                f"执行Redis命令失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    def get_stats(self) -> PoolStats:
        """获取连接池统计信息"""
        if hasattr(self._pool, 'created_connections'):
            self._stats.total_connections = self._pool.created_connections
        
        return self._stats
    
    def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        try:
            client = self.get_client()
            start_time = time.time()
            client.ping()
            response_time = time.time() - start_time
            
            return {
                "status": "healthy",
                "host": self.config.host,
                "port": self.config.port,
                "db": self.config.db,
                "response_time": response_time,
                "cluster_enabled": self.config.cluster_enabled,
                "sentinel_enabled": self.config.sentinel_enabled
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "host": self.config.host,
                "port": self.config.port
            }
    
    def close(self):
        """关闭连接池"""
        try:
            if self._pool:
                if hasattr(self._pool, 'disconnect'):
                    self._pool.disconnect()
                elif hasattr(self._pool, 'connection_pool'):
                    self._pool.connection_pool.disconnect()
            
            self.logger.info("Redis连接池已关闭")
        except Exception as e:
            self.logger.warning(f"关闭Redis连接池时发生错误: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.close()
        except:
            pass
