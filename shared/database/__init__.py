"""
数据库连接模块

提供项目的统一数据库连接管理：
- MongoDB连接池管理
- Milvus客户端池管理
- Redis连接池管理
"""

from .mongodb import MongoDBPool, MongoDBOperations, MongoDBQueryBuilder
from .milvus import MilvusPool, MilvusVectorOperations, MilvusCollectionManager
from .redis import RedisPool

__all__ = [
    # MongoDB
    "MongoDBPool",
    "MongoDBOperations",
    "MongoDBQueryBuilder",

    # Milvus
    "MilvusPool",
    "MilvusVectorOperations",
    "MilvusCollectionManager",

    # Redis
    "RedisPool"
]
