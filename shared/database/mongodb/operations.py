#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB通用操作类

提供MongoDB的统一数据库操作功能：
- CRUD操作封装
- 批量操作优化
- 事务支持
- 分片操作
- 聚合查询

作者: User-DF Team
版本: 2.0.0
"""

import time
from typing import Dict, List, Any, Optional, Union, Iterator, Tuple
from dataclasses import dataclass
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import BulkWriteError, DuplicateKeyError
from pymongo import UpdateOne, InsertOne, DeleteOne, ReplaceOne
import logging

from ...core import Logger, ExceptionHandler, MongoDBException, ErrorCode, DataValidator
from .connection_pool import MongoDBPool


@dataclass
class OperationResult:
    """操作结果"""
    success: bool
    affected_count: int = 0
    inserted_count: int = 0
    modified_count: int = 0
    deleted_count: int = 0
    upserted_count: int = 0
    error_message: Optional[str] = None
    execution_time: float = 0.0
    details: Optional[Dict[str, Any]] = None


@dataclass
class QueryOptions:
    """查询选项"""
    projection: Optional[Dict[str, Any]] = None
    sort: Optional[List[Tuple[str, int]]] = None
    limit: Optional[int] = None
    skip: Optional[int] = None
    batch_size: Optional[int] = None
    hint: Optional[Union[str, List[Tuple[str, int]]]] = None


class MongoDBOperations:
    """MongoDB通用操作类"""

    def __init__(self, pool: MongoDBPool, collection_name: str, provid: Optional[int] = None):
        """
        初始化MongoDB操作类

        Args:
            pool: MongoDB连接池
            collection_name: 基础集合名称
            provid: 省份ID，如果提供则会在集合名称后添加后缀
        """
        self.pool = pool
        self.base_collection_name = collection_name
        self.provid = provid

        # 构建完整的集合名称
        self.collection_name = self._build_collection_name(collection_name, provid)
        self.logger = Logger.get_logger(f"MongoDBOperations.{self.collection_name}")

        # 获取集合对象
        self.collection = self.pool.get_collection(collection_name, provid=provid)

        # 操作统计
        self.stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "total_execution_time": 0.0
        }

        self.logger.debug(f"MongoDB操作类初始化完成: {self.collection_name}")

    def _build_collection_name(self, base_name: str, provid: Optional[int] = None) -> str:
        """
        构建集合名称

        Args:
            base_name: 基础集合名称
            provid: 省份ID

        Returns:
            完整的集合名称
        """
        if provid is not None:
            return f"{base_name}_{provid}"
        return base_name
    
    def insert_one(self, document: Dict[str, Any], 
                   validate: bool = True) -> OperationResult:
        """
        插入单个文档
        
        Args:
            document: 要插入的文档
            validate: 是否验证文档
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            # 数据验证
            if validate:
                self._validate_document(document)
            
            result = self.collection.insert_one(document)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return OperationResult(
                success=True,
                inserted_count=1,
                execution_time=execution_time,
                details={"inserted_id": str(result.inserted_id)}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"插入文档失败: {e}"
            self.logger.error(error_msg)
            
            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def insert_many(self, documents: List[Dict[str, Any]],
                    ordered: bool = None,
                    validate: bool = True,
                    bypass_document_validation: bool = None) -> OperationResult:
        """
        批量插入文档

        Args:
            documents: 要插入的文档列表
            ordered: 是否有序插入
            validate: 是否验证文档
            bypass_document_validation: 是否跳过文档验证

        Returns:
            操作结果
        """
        start_time = time.time()

        try:
            # 获取写入优化配置
            write_config = self.pool.get_write_optimization_config()

            # 如果没有显式指定ordered，使用配置的unordered_inserts设置
            if ordered is None:
                ordered = not write_config["bulk_operations"]["unordered_inserts"]

            # 如果没有显式指定bypass_document_validation，使用配置值
            if bypass_document_validation is None:
                bypass_document_validation = write_config["bulk_operations"]["bypass_document_validation"]

            # 数据验证
            if validate:
                for doc in documents:
                    self._validate_document(doc)

            result = self.collection.insert_many(
                documents,
                ordered=ordered,
                bypass_document_validation=bypass_document_validation
            )
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return OperationResult(
                success=True,
                inserted_count=len(result.inserted_ids),
                execution_time=execution_time,
                details={"inserted_ids": [str(id) for id in result.inserted_ids]}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"批量插入文档失败: {e}"
            self.logger.error(error_msg)
            
            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def find_one(self, filter_dict: Dict[str, Any], 
                 options: Optional[QueryOptions] = None) -> Optional[Dict[str, Any]]:
        """
        查找单个文档
        
        Args:
            filter_dict: 查询过滤条件
            options: 查询选项
            
        Returns:
            文档或None
        """
        start_time = time.time()
        
        try:
            projection = options.projection if options else None
            result = self.collection.find_one(filter_dict, projection)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            self.logger.error(f"查找文档失败: {e}")
            raise MongoDBException(
                f"查找文档失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )
    
    def find_many(self, filter_dict: Dict[str, Any], 
                  options: Optional[QueryOptions] = None) -> Iterator[Dict[str, Any]]:
        """
        查找多个文档
        
        Args:
            filter_dict: 查询过滤条件
            options: 查询选项
            
        Yields:
            文档迭代器
        """
        start_time = time.time()
        
        try:
            # 构建find参数
            find_kwargs = {}
            if options and options.projection:
                find_kwargs['projection'] = options.projection
            
            cursor = self.collection.find(filter_dict, **find_kwargs)
            
            # 应用其他查询选项
            if options:
                if options.sort:
                    cursor = cursor.sort(options.sort)
                if options.skip:
                    cursor = cursor.skip(options.skip)
                if options.limit:
                    cursor = cursor.limit(options.limit)
                if options.batch_size:
                    cursor = cursor.batch_size(options.batch_size)
                if options.hint:
                    cursor = cursor.hint(options.hint)
            
            for document in cursor:
                yield document
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            self.logger.error(f"查找文档失败: {e}")
            raise MongoDBException(
                f"查找文档失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )
    
    def update_one(self, filter_dict: Dict[str, Any], 
                   update_dict: Dict[str, Any],
                   upsert: bool = False) -> OperationResult:
        """
        更新单个文档
        
        Args:
            filter_dict: 查询过滤条件
            update_dict: 更新操作
            upsert: 是否启用upsert
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            result = self.collection.update_one(filter_dict, update_dict, upsert=upsert)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return OperationResult(
                success=True,
                affected_count=result.matched_count,
                modified_count=result.modified_count,
                upserted_count=1 if result.upserted_id else 0,
                execution_time=execution_time,
                details={"upserted_id": str(result.upserted_id) if result.upserted_id else None}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"更新文档失败: {e}"
            self.logger.error(error_msg)
            
            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def update_many(self, filter_dict: Dict[str, Any], 
                    update_dict: Dict[str, Any],
                    upsert: bool = False) -> OperationResult:
        """
        更新多个文档
        
        Args:
            filter_dict: 查询过滤条件
            update_dict: 更新操作
            upsert: 是否启用upsert
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            result = self.collection.update_many(filter_dict, update_dict, upsert=upsert)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return OperationResult(
                success=True,
                affected_count=result.matched_count,
                modified_count=result.modified_count,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"批量更新文档失败: {e}"
            self.logger.error(error_msg)
            
            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def delete_one(self, filter_dict: Dict[str, Any]) -> OperationResult:
        """
        删除单个文档
        
        Args:
            filter_dict: 查询过滤条件
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            result = self.collection.delete_one(filter_dict)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return OperationResult(
                success=True,
                deleted_count=result.deleted_count,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"删除文档失败: {e}"
            self.logger.error(error_msg)
            
            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def _validate_document(self, document: Dict[str, Any]):
        """验证文档格式"""
        if not isinstance(document, dict):
            raise MongoDBException(
                "文档必须是字典类型",
                ErrorCode.DATA_VALIDATION_ERROR
            )
    
    def _update_stats(self, success: bool, execution_time: float):
        """更新操作统计"""
        self.stats["total_operations"] += 1
        self.stats["total_execution_time"] += execution_time
        
        if success:
            self.stats["successful_operations"] += 1
        else:
            self.stats["failed_operations"] += 1
    
    def bulk_write(self, operations: List[Union[InsertOne, UpdateOne, DeleteOne, ReplaceOne]],
                   ordered: bool = False, bypass_document_validation: bool = None) -> OperationResult:
        """
        批量写入操作

        Args:
            operations: 操作列表
            ordered: 是否有序执行
            bypass_document_validation: 是否跳过文档验证

        Returns:
            操作结果
        """
        start_time = time.time()

        try:
            # 获取写入优化配置
            write_config = self.pool.get_write_optimization_config()

            # 如果没有显式指定bypass_document_validation，使用配置值
            if bypass_document_validation is None:
                bypass_document_validation = write_config["bulk_operations"]["bypass_document_validation"]

            # 如果没有显式指定ordered，使用配置的unordered_inserts设置
            if ordered is False:  # 只有当明确指定为False时才使用配置
                ordered = not write_config["bulk_operations"]["unordered_inserts"]

            result = self.collection.bulk_write(
                operations,
                ordered=ordered,
                bypass_document_validation=bypass_document_validation
            )

            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)

            return OperationResult(
                success=True,
                inserted_count=result.inserted_count,
                modified_count=result.modified_count,
                deleted_count=result.deleted_count,
                upserted_count=result.upserted_count,
                execution_time=execution_time,
                details={
                    "bulk_api_result": {
                        "acknowledged": result.acknowledged,
                        "upserted_ids": {str(k): str(v) for k, v in result.upserted_ids.items()}
                    }
                }
            )

        except BulkWriteError as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            # 处理部分成功的批量操作
            result = e.details
            error_msg = f"批量写入部分失败: {len(result.get('writeErrors', []))} 个错误"
            self.logger.warning(error_msg)

            return OperationResult(
                success=False,
                inserted_count=result.get('nInserted', 0),
                modified_count=result.get('nModified', 0),
                deleted_count=result.get('nRemoved', 0),
                upserted_count=result.get('nUpserted', 0),
                error_message=error_msg,
                execution_time=execution_time,
                details={"bulk_write_errors": result.get('writeErrors', [])}
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            error_msg = f"批量写入操作失败: {e}"
            self.logger.error(error_msg)

            return OperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )

    def aggregate(self, pipeline: List[Dict[str, Any]],
                  allow_disk_use: bool = False,
                  batch_size: Optional[int] = None) -> Iterator[Dict[str, Any]]:
        """
        聚合查询

        Args:
            pipeline: 聚合管道
            allow_disk_use: 是否允许使用磁盘
            batch_size: 批次大小

        Yields:
            聚合结果迭代器
        """
        start_time = time.time()

        try:
            cursor = self.collection.aggregate(
                pipeline,
                allowDiskUse=allow_disk_use,
                batchSize=batch_size
            )

            for document in cursor:
                yield document

            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            self.logger.error(f"聚合查询失败: {e}")
            raise MongoDBException(
                f"聚合查询失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )

    def count_documents(self, filter_dict: Dict[str, Any]) -> int:
        """
        统计文档数量

        Args:
            filter_dict: 查询过滤条件

        Returns:
            文档数量
        """
        start_time = time.time()

        try:
            count = self.collection.count_documents(filter_dict)

            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)

            return count

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            self.logger.error(f"统计文档数量失败: {e}")
            raise MongoDBException(
                f"统计文档数量失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )

    def create_index(self, keys: Union[str, List[Tuple[str, int]]],
                     **kwargs) -> str:
        """
        创建索引

        Args:
            keys: 索引键
            **kwargs: 索引选项

        Returns:
            索引名称
        """
        try:
            index_name = self.collection.create_index(keys, **kwargs)
            self.logger.info(f"创建索引成功: {index_name}")
            return index_name

        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            raise MongoDBException(
                f"创建索引失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )

    def drop_index(self, index_name: str):
        """
        删除索引

        Args:
            index_name: 索引名称
        """
        try:
            self.collection.drop_index(index_name)
            self.logger.info(f"删除索引成功: {index_name}")

        except Exception as e:
            self.logger.error(f"删除索引失败: {e}")
            raise MongoDBException(
                f"删除索引失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )

    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            stats = self.pool.get_database().command("collStats", self.collection_name)
            return stats

        except Exception as e:
            self.logger.error(f"获取集合统计信息失败: {e}")
            raise MongoDBException(
                f"获取集合统计信息失败: {e}",
                ErrorCode.DATABASE_QUERY_ERROR
            )

    def get_stats(self) -> Dict[str, Any]:
        """获取操作统计"""
        stats = self.stats.copy()
        if stats["total_operations"] > 0:
            stats["average_execution_time"] = stats["total_execution_time"] / stats["total_operations"]
            stats["success_rate"] = stats["successful_operations"] / stats["total_operations"]
        else:
            stats["average_execution_time"] = 0.0
            stats["success_rate"] = 0.0

        return stats

    async def bulk_upsert_async(self, user_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        异步批量upsert操作

        Args:
            user_data: 用户数据列表

        Returns:
            操作结果字典
        """
        try:
            operations = []

            for user in user_data:
                # 使用_id作为查询条件进行upsert
                uid = user.get("uid") or user.get("_id")
                if uid is None:
                    self.logger.warning(f"用户数据缺少uid字段: {user}")
                    continue

                # 构建upsert操作
                filter_dict = {"_id": uid}

                # 准备更新数据，移除provid字段（因为现在用集合名称区分）
                update_data = user.copy()
                update_data.pop("provid", None)  # 移除provid字段
                update_data.pop("prov_id", None)  # 移除prov_id字段

                # 设置_id字段
                update_data["_id"] = uid

                # 创建upsert操作
                operation = UpdateOne(
                    filter_dict,
                    {"$set": update_data},
                    upsert=True
                )
                operations.append(operation)

            if not operations:
                return {
                    "inserted_count": 0,
                    "modified_count": 0,
                    "matched_count": 0
                }

            # 执行批量操作
            result = self.bulk_write(operations, ordered=False)

            return {
                "inserted_count": result.inserted_count + result.upserted_count,
                "modified_count": result.modified_count,
                "matched_count": result.inserted_count + result.modified_count + result.upserted_count
            }

        except Exception as e:
            self.logger.error(f"批量upsert操作失败: {e}")
            raise
