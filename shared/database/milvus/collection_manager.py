#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus集合管理器

提供Milvus集合的管理功能：
- 集合创建和删除
- 索引管理
- 分区管理
- 集合信息查询

作者: User-DF Team
版本: 2.0.0
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

from ...core import Logger, ExceptionHandler, MilvusException, ErrorCode
from .client_pool import MilvusPool


@dataclass
class CollectionSchema:
    """集合模式定义"""
    collection_name: str
    dimension: int
    description: str = ""
    metric_type: str = "COSINE"
    index_type: str = "IVF_FLAT"
    index_params: Optional[Dict[str, Any]] = None
    auto_id: bool = True
    primary_field: str = "id"
    vector_field: str = "vector"


@dataclass
class CollectionInfo:
    """集合信息"""
    name: str
    description: str
    num_entities: int
    dimension: int
    metric_type: str
    index_type: str
    consistency_level: str
    created_time: str


class MilvusCollectionManager:
    """Milvus集合管理器"""
    
    def __init__(self, pool: MilvusPool):
        """
        初始化Milvus集合管理器
        
        Args:
            pool: Milvus客户端池
        """
        self.pool = pool
        self.logger = Logger.get_logger("MilvusCollectionManager")
        
        self.logger.info("Milvus集合管理器初始化完成")
    
    def create_collection(self, schema: CollectionSchema) -> bool:
        """
        创建集合
        
        Args:
            schema: 集合模式
            
        Returns:
            创建是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 检查集合是否已存在
            if self.collection_exists(schema.collection_name):
                self.logger.warning(f"集合 {schema.collection_name} 已存在")
                return True
            
            # 构建集合模式
            collection_schema = {
                "collection_name": schema.collection_name,
                "dimension": schema.dimension,
                "description": schema.description,
                "metric_type": schema.metric_type,
                "auto_id": schema.auto_id,
                "primary_field_name": schema.primary_field,
                "vector_field_name": schema.vector_field
            }
            
            # 创建集合
            client_info.client.create_collection(**collection_schema)
            
            # 创建索引
            if schema.index_type and schema.index_type != "FLAT":
                self.create_index(
                    collection_name=schema.collection_name,
                    field_name=schema.vector_field,
                    index_type=schema.index_type,
                    index_params=schema.index_params or {}
                )
            
            self.logger.info(f"创建集合成功: {schema.collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建集合失败: {e}")
            raise MilvusException(
                f"创建集合失败: {e}",
                ErrorCode.MILVUS_COLLECTION_NOT_FOUND
            )
    
    def drop_collection(self, collection_name: str) -> bool:
        """
        删除集合
        
        Args:
            collection_name: 集合名称
            
        Returns:
            删除是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 检查集合是否存在
            if not self.collection_exists(collection_name):
                self.logger.warning(f"集合 {collection_name} 不存在")
                return True
            
            # 删除集合
            client_info.client.drop_collection(collection_name=collection_name)
            
            self.logger.info(f"删除集合成功: {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除集合失败: {e}")
            raise MilvusException(
                f"删除集合失败: {e}",
                ErrorCode.MILVUS_COLLECTION_NOT_FOUND
            )
    
    def collection_exists(self, collection_name: str) -> bool:
        """
        检查集合是否存在
        
        Args:
            collection_name: 集合名称
            
        Returns:
            集合是否存在
        """
        try:
            client_info = self.pool.get_client()
            return client_info.client.has_collection(collection_name=collection_name)
            
        except Exception as e:
            self.logger.error(f"检查集合存在性失败: {e}")
            return False
    
    def list_collections(self) -> List[str]:
        """
        列出所有集合
        
        Returns:
            集合名称列表
        """
        try:
            client_info = self.pool.get_client()
            return client_info.client.list_collections()
            
        except Exception as e:
            self.logger.error(f"列出集合失败: {e}")
            raise MilvusException(
                f"列出集合失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def get_collection_info(self, collection_name: str) -> CollectionInfo:
        """
        获取集合信息
        
        Args:
            collection_name: 集合名称
            
        Returns:
            集合信息
        """
        try:
            client_info = self.pool.get_client()
            
            # 获取集合描述
            desc = client_info.client.describe_collection(collection_name=collection_name)
            
            # 获取集合统计信息
            stats = client_info.client.get_collection_stats(collection_name=collection_name)
            
            return CollectionInfo(
                name=collection_name,
                description=desc.get("description", ""),
                num_entities=stats.get("row_count", 0),
                dimension=desc.get("dimension", 0),
                metric_type=desc.get("metric_type", ""),
                index_type=desc.get("index_type", ""),
                consistency_level=desc.get("consistency_level", ""),
                created_time=desc.get("created_time", "")
            )
            
        except Exception as e:
            self.logger.error(f"获取集合信息失败: {e}")
            raise MilvusException(
                f"获取集合信息失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def create_index(self, collection_name: str, field_name: str, 
                    index_type: str = "IVF_FLAT",
                    index_params: Optional[Dict[str, Any]] = None) -> bool:
        """
        创建索引
        
        Args:
            collection_name: 集合名称
            field_name: 字段名称
            index_type: 索引类型
            index_params: 索引参数
            
        Returns:
            创建是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 默认索引参数
            if index_params is None:
                if index_type == "IVF_FLAT":
                    index_params = {"nlist": 1024}
                elif index_type == "IVF_SQ8":
                    index_params = {"nlist": 1024}
                elif index_type == "IVF_PQ":
                    index_params = {"nlist": 1024, "m": 16, "nbits": 8}
                elif index_type == "HNSW":
                    index_params = {"M": 16, "efConstruction": 200}
                else:
                    index_params = {}
            
            # 构建索引参数
            index_spec = {
                "collection_name": collection_name,
                "field_name": field_name,
                "index_type": index_type,
                "index_params": index_params
            }
            
            # 创建索引
            client_info.client.create_index(**index_spec)
            
            self.logger.info(f"创建索引成功: {collection_name}.{field_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            raise MilvusException(
                f"创建索引失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def drop_index(self, collection_name: str, field_name: str) -> bool:
        """
        删除索引
        
        Args:
            collection_name: 集合名称
            field_name: 字段名称
            
        Returns:
            删除是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 删除索引
            client_info.client.drop_index(
                collection_name=collection_name,
                field_name=field_name
            )
            
            self.logger.info(f"删除索引成功: {collection_name}.{field_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除索引失败: {e}")
            raise MilvusException(
                f"删除索引失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def create_partition(self, collection_name: str, partition_name: str) -> bool:
        """
        创建分区
        
        Args:
            collection_name: 集合名称
            partition_name: 分区名称
            
        Returns:
            创建是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 创建分区
            client_info.client.create_partition(
                collection_name=collection_name,
                partition_name=partition_name
            )
            
            self.logger.info(f"创建分区成功: {collection_name}.{partition_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建分区失败: {e}")
            raise MilvusException(
                f"创建分区失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def drop_partition(self, collection_name: str, partition_name: str) -> bool:
        """
        删除分区
        
        Args:
            collection_name: 集合名称
            partition_name: 分区名称
            
        Returns:
            删除是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 删除分区
            client_info.client.drop_partition(
                collection_name=collection_name,
                partition_name=partition_name
            )
            
            self.logger.info(f"删除分区成功: {collection_name}.{partition_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除分区失败: {e}")
            raise MilvusException(
                f"删除分区失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def list_partitions(self, collection_name: str) -> List[str]:
        """
        列出分区
        
        Args:
            collection_name: 集合名称
            
        Returns:
            分区名称列表
        """
        try:
            client_info = self.pool.get_client()
            return client_info.client.list_partitions(collection_name=collection_name)
            
        except Exception as e:
            self.logger.error(f"列出分区失败: {e}")
            raise MilvusException(
                f"列出分区失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def load_collection(self, collection_name: str) -> bool:
        """
        加载集合到内存
        
        Args:
            collection_name: 集合名称
            
        Returns:
            加载是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 加载集合
            client_info.client.load_collection(collection_name=collection_name)
            
            self.logger.info(f"加载集合成功: {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载集合失败: {e}")
            raise MilvusException(
                f"加载集合失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
    
    def release_collection(self, collection_name: str) -> bool:
        """
        释放集合内存
        
        Args:
            collection_name: 集合名称
            
        Returns:
            释放是否成功
        """
        try:
            client_info = self.pool.get_client()
            
            # 释放集合
            client_info.client.release_collection(collection_name=collection_name)
            
            self.logger.info(f"释放集合成功: {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"释放集合失败: {e}")
            raise MilvusException(
                f"释放集合失败: {e}",
                ErrorCode.MILVUS_QUERY_ERROR
            )
