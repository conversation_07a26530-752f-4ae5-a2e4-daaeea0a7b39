#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus向量操作类

提供Milvus向量数据库的统一操作功能：
- 向量插入和更新
- 向量搜索和查询
- 批量操作优化
- 错误处理和重试

作者: User-DF Team
版本: 2.0.0
"""

import time
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import numpy as np

from ...core import Logger, ExceptionHandler, MilvusException, ErrorCode, DataValidator
from .client_pool import MilvusPool


@dataclass
class VectorSearchResult:
    """向量搜索结果"""
    success: bool
    results: List[Dict[str, Any]] = None
    total_count: int = 0
    execution_time: float = 0.0
    error_message: Optional[str] = None


@dataclass
class VectorOperationResult:
    """向量操作结果"""
    success: bool
    affected_count: int = 0
    inserted_count: int = 0
    updated_count: int = 0
    deleted_count: int = 0
    execution_time: float = 0.0
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class MilvusVectorOperations:
    """Milvus向量操作类"""
    
    def __init__(self, pool: MilvusPool, collection_name: str):
        """
        初始化Milvus向量操作类
        
        Args:
            pool: Milvus客户端池
            collection_name: 集合名称
        """
        self.pool = pool
        self.collection_name = collection_name
        self.logger = Logger.get_logger(f"MilvusVectorOperations.{collection_name}")
        
        # 操作统计
        self.stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "total_execution_time": 0.0
        }
        
        self.logger.info(f"Milvus向量操作类初始化完成: {collection_name}")
    
    def insert_vectors(self, data: List[Dict[str, Any]], 
                      partition_name: Optional[str] = None) -> VectorOperationResult:
        """
        插入向量数据
        
        Args:
            data: 要插入的数据列表
            partition_name: 分区名称
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            # 数据验证
            self._validate_insert_data(data)
            
            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)
            
            # 构建插入参数
            insert_params = {
                "collection_name": self.collection_name,
                "data": data
            }
            
            if partition_name:
                insert_params["partition_name"] = partition_name
            
            # 执行插入
            result = client_info.client.insert(**insert_params)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return VectorOperationResult(
                success=True,
                inserted_count=len(data),
                execution_time=execution_time,
                details={"insert_result": result}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"插入向量数据失败: {e}"
            self.logger.error(error_msg)
            
            return VectorOperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def upsert_vectors(self, data: List[Dict[str, Any]], 
                      partition_name: Optional[str] = None) -> VectorOperationResult:
        """
        更新插入向量数据
        
        Args:
            data: 要更新插入的数据列表
            partition_name: 分区名称
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            # 数据验证
            self._validate_insert_data(data)
            
            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)
            
            # 构建upsert参数
            upsert_params = {
                "collection_name": self.collection_name,
                "data": data
            }
            
            if partition_name:
                upsert_params["partition_name"] = partition_name
            
            # 执行upsert
            result = client_info.client.upsert(**upsert_params)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return VectorOperationResult(
                success=True,
                updated_count=len(data),
                execution_time=execution_time,
                details={"upsert_result": result}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"更新插入向量数据失败: {e}"
            self.logger.error(error_msg)
            
            return VectorOperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def search_vectors(self, query_vectors: List[List[float]], 
                      limit: int = 10,
                      search_params: Optional[Dict[str, Any]] = None,
                      output_fields: Optional[List[str]] = None,
                      filter_expr: Optional[str] = None,
                      partition_names: Optional[List[str]] = None) -> VectorSearchResult:
        """
        搜索向量
        
        Args:
            query_vectors: 查询向量列表
            limit: 返回结果数量限制
            search_params: 搜索参数
            output_fields: 输出字段
            filter_expr: 过滤表达式
            partition_names: 分区名称列表
            
        Returns:
            搜索结果
        """
        start_time = time.time()
        
        try:
            # 数据验证
            self._validate_query_vectors(query_vectors)

            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)

            # 确保集合已加载
            try:
                client_info.client.load_collection(self.collection_name)
                self.logger.debug(f"集合 {self.collection_name} 已加载")
            except Exception as load_error:
                self.logger.warning(f"加载集合失败: {load_error}")
                # 继续尝试搜索，可能集合已经加载

            # 构建搜索参数
            search_params_dict = {
                "collection_name": self.collection_name,
                "data": query_vectors,
                "limit": limit
            }
            
            if search_params:
                search_params_dict["search_params"] = search_params
            
            if output_fields:
                search_params_dict["output_fields"] = output_fields
            
            if filter_expr:
                search_params_dict["filter"] = filter_expr
            
            if partition_names:
                search_params_dict["partition_names"] = partition_names
            
            # 执行搜索
            results = client_info.client.search(**search_params_dict)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            # 处理搜索结果
            processed_results = self._process_search_results(results)
            
            return VectorSearchResult(
                success=True,
                results=processed_results,
                total_count=len(processed_results),
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"搜索向量失败: {e}"
            self.logger.error(error_msg)
            
            return VectorSearchResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def query_vectors(self, ids: List[Union[int, str]],
                     output_fields: Optional[List[str]] = None,
                     partition_names: Optional[List[str]] = None) -> VectorSearchResult:
        """
        查询向量数据

        通过ID列表查询向量数据，不限制ID数量大小
        注意：根据ID查询不需要使用迭代器，可以直接查询任意数量的ID

        Args:
            ids: ID列表
            output_fields: 输出字段
            partition_names: 分区名称列表

        Returns:
            查询结果
        """
        # 直接使用ID查询，不限制数量大小
        return self._query_vectors_by_ids(ids, output_fields, partition_names)

    def _detect_id_field(self, output_fields: Optional[List[str]]) -> str:
        """
        检测ID字段名称

        Args:
            output_fields: 输出字段列表

        Returns:
            ID字段名称
        """
        # 常见的ID字段名优先级
        common_id_fields = ["item_id", "id", "pid", "_id"]

        if output_fields:
            # 从输出字段中查找ID字段
            for field in common_id_fields:
                if field in output_fields:
                    return field

        # 默认使用item_id（根据配置中的集合结构）
        return "item_id"

    def _build_id_filter(self, ids: List[Union[int, str]], id_field: str) -> str:
        """
        构建ID过滤表达式

        Args:
            ids: ID列表
            id_field: ID字段名称

        Returns:
            过滤表达式
        """
        # 将ID列表转换为字符串，支持大量ID的查询
        if len(ids) == 1:
            return f"{id_field} == {ids[0]}"

        # 对于大量ID，使用in操作符
        # 注意：Milvus的in操作符有长度限制，这里分批处理
        batch_size = 1000  # 每批1000个ID
        conditions = []

        for i in range(0, len(ids), batch_size):
            batch_ids = ids[i:i + batch_size]
            if len(batch_ids) == 1:
                conditions.append(f"{id_field} == {batch_ids[0]}")
            else:
                id_list_str = ", ".join(str(id_val) for id_val in batch_ids)
                conditions.append(f"{id_field} in [{id_list_str}]")

        # 使用OR连接多个条件
        return " or ".join(f"({condition})" for condition in conditions)

    def _query_vectors_by_ids(self, ids: List[Union[int, str]],
                             output_fields: Optional[List[str]] = None,
                             partition_names: Optional[List[str]] = None) -> VectorSearchResult:
        """
        通过ID直接查询向量数据（原有方法）

        Args:
            ids: ID列表
            output_fields: 输出字段
            partition_names: 分区名称列表

        Returns:
            查询结果
        """
        start_time = time.time()

        try:
            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)

            # 确保集合已加载
            try:
                client_info.client.load_collection(self.collection_name)
                self.logger.debug(f"集合 {self.collection_name} 已加载")
            except Exception as load_error:
                self.logger.warning(f"加载集合失败: {load_error}")
                # 继续尝试查询，可能集合已经加载

            # 记录查询参数用于调试
            self.logger.debug(f"开始查询向量，集合: {self.collection_name}, ID数量: {len(ids)}")
            self.logger.debug(f"查询ID样例: {ids[:5] if len(ids) > 5 else ids}")

            # 构建查询参数
            query_params = {
                "collection_name": self.collection_name,
                "ids": ids
            }

            # 设置默认输出字段 - 支持常见的ID字段名
            if output_fields is None:
                # 尝试常见的ID字段名，让Milvus自动选择存在的字段
                output_fields = ["id", "item_id", "pid", "_id"]

            if output_fields:
                query_params["output_fields"] = output_fields
                self.logger.debug(f"输出字段: {output_fields}")

            if partition_names:
                query_params["partition_names"] = partition_names
                self.logger.debug(f"分区名称: {partition_names}")

            # 执行查询
            self.logger.debug(f"执行Milvus查询，参数: {query_params}")
            results = client_info.client.get(**query_params)

            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)

            # 详细记录查询结果
            self.logger.debug(f"查询完成，耗时: {execution_time:.3f}秒")
            self.logger.debug(f"结果类型: {type(results)}")

            if results:
                self.logger.debug(f"结果数量: {len(results)}")
                if len(results) > 0:
                    self.logger.debug(f"第一个结果样例: {results[0]}")
            else:
                self.logger.debug("查询结果为空")

            return VectorSearchResult(
                success=True,
                results=results,
                total_count=len(results) if results else 0,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            error_msg = f"查询向量数据失败: {e}"
            self.logger.error(error_msg)
            self.logger.error(f"查询参数: 集合={self.collection_name}, ID数量={len(ids)}")

            # 记录详细的异常信息
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")

            return VectorSearchResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )

    def query_all_vectors(self,
                         filter_expr: str = "",
                         output_fields: Optional[List[str]] = None,
                         batch_size: int = 10000,
                         max_results: Optional[int] = None,
                         partition_names: Optional[List[str]] = None) -> VectorSearchResult:
        """
        查询所有向量数据，支持大数据集迭代查询

        当查询数量大于16384时，自动使用查询迭代器进行分批查询

        Args:
            filter_expr: 过滤表达式
            output_fields: 输出字段
            batch_size: 批次大小
            max_results: 最大结果数量，None表示无限制
            partition_names: 分区名称列表

        Returns:
            查询结果
        """
        start_time = time.time()

        try:
            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)

            # 记录查询参数
            self.logger.debug(f"开始查询所有向量，集合: {self.collection_name}")
            self.logger.debug(f"过滤条件: {filter_expr}, 批次大小: {batch_size}, 最大结果: {max_results}")

            # 如果max_results小于等于16384，使用普通查询
            if max_results and max_results <= 16384:
                return self._query_vectors_simple(
                    filter_expr, output_fields, max_results, partition_names
                )

            # 使用查询迭代器进行大数据集查询
            all_results = []
            total_fetched = 0

            # 确保集合已加载
            client_info.client.load_collection(self.collection_name)

            # 创建查询迭代器
            query_iterator = client_info.client.query_iterator(
                collection_name=self.collection_name,
                filter=filter_expr,
                output_fields=output_fields or [],
                batch_size=min(batch_size, 16384),  # 确保不超过Milvus限制
                partition_names=partition_names
            )

            try:
                # 迭代获取结果
                while True:
                    # 检查是否达到最大结果数
                    if max_results and total_fetched >= max_results:
                        break

                    # 获取下一批数据
                    batch = query_iterator.next()
                    if not batch:
                        self.logger.debug("没有更多数据可获取")
                        break

                    # 处理当前批次
                    for entity in batch:
                        if max_results and total_fetched >= max_results:
                            break
                        all_results.append(entity)
                        total_fetched += 1

                    self.logger.debug(f"已获取 {total_fetched} 条记录")

            finally:
                # 关闭迭代器
                query_iterator.close()
                # 释放集合
                client_info.client.release_collection(self.collection_name)

            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)

            self.logger.info(f"查询完成，总共获取 {len(all_results)} 条记录，耗时: {execution_time:.3f}秒")

            return VectorSearchResult(
                success=True,
                results=all_results,
                total_count=len(all_results),
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)

            error_msg = f"查询所有向量数据失败: {e}"
            self.logger.error(error_msg)

            # 记录详细的异常信息
            import traceback
            self.logger.error(f"异常堆栈: {traceback.format_exc()}")

            return VectorSearchResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )

    def _query_vectors_simple(self,
                             filter_expr: str,
                             output_fields: Optional[List[str]],
                             limit: int,
                             partition_names: Optional[List[str]]) -> VectorSearchResult:
        """
        简单查询向量数据（用于小数据集）

        Args:
            filter_expr: 过滤表达式
            output_fields: 输出字段
            limit: 限制数量
            partition_names: 分区名称列表

        Returns:
            查询结果
        """
        client_info = self.pool.get_client()

        # 确保集合已加载
        try:
            client_info.client.load_collection(self.collection_name)
            self.logger.debug(f"集合 {self.collection_name} 已加载")
        except Exception as load_error:
            self.logger.warning(f"加载集合失败: {load_error}")
            # 继续尝试查询，可能集合已经加载

        # 构建查询参数
        query_params = {
            "collection_name": self.collection_name,
            "filter": filter_expr,
            "limit": limit
        }

        if output_fields:
            query_params["output_fields"] = output_fields

        if partition_names:
            query_params["partition_names"] = partition_names

        # 执行查询
        results = client_info.client.query(**query_params)

        return VectorSearchResult(
            success=True,
            results=results or [],
            total_count=len(results) if results else 0,
            execution_time=0.0
        )
    
    def delete_vectors(self, ids: List[Union[int, str]], 
                      partition_name: Optional[str] = None) -> VectorOperationResult:
        """
        删除向量数据
        
        Args:
            ids: 要删除的ID列表
            partition_name: 分区名称
            
        Returns:
            操作结果
        """
        start_time = time.time()
        
        try:
            client_info = self.pool.get_client()
            if client_info is None:
                raise MilvusException("无法获取Milvus客户端，服务可能不可用", ErrorCode.MILVUS_CONNECTION_ERROR)
            
            # 构建删除参数
            delete_params = {
                "collection_name": self.collection_name,
                "ids": ids
            }
            
            if partition_name:
                delete_params["partition_name"] = partition_name
            
            # 执行删除
            result = client_info.client.delete(**delete_params)
            
            execution_time = time.time() - start_time
            self._update_stats(True, execution_time)
            
            return VectorOperationResult(
                success=True,
                deleted_count=len(ids),
                execution_time=execution_time,
                details={"delete_result": result}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_stats(False, execution_time)
            
            error_msg = f"删除向量数据失败: {e}"
            self.logger.error(error_msg)
            
            return VectorOperationResult(
                success=False,
                error_message=error_msg,
                execution_time=execution_time
            )
    
    def _validate_insert_data(self, data: List[Dict[str, Any]]):
        """验证插入数据"""
        if not isinstance(data, list) or not data:
            raise MilvusException(
                "插入数据必须是非空列表",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                raise MilvusException(
                    f"插入数据第 {i} 项必须是字典类型",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
    
    def _validate_query_vectors(self, query_vectors: List[List[float]]):
        """验证查询向量"""
        if not isinstance(query_vectors, list) or not query_vectors:
            raise MilvusException(
                "查询向量必须是非空列表",
                ErrorCode.DATA_VALIDATION_ERROR
            )
        
        for i, vector in enumerate(query_vectors):
            if not isinstance(vector, (list, np.ndarray)):
                raise MilvusException(
                    f"查询向量第 {i} 项必须是列表或numpy数组",
                    ErrorCode.DATA_VALIDATION_ERROR
                )
            
            # 检查向量中的值
            for j, value in enumerate(vector):
                if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                    raise MilvusException(
                        f"查询向量第 {i} 项第 {j} 个值无效: {value}",
                        ErrorCode.DATA_VALIDATION_ERROR
                    )
    
    def _process_search_results(self, results: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """处理搜索结果"""
        processed_results = []
        
        for query_results in results:
            for result in query_results:
                processed_results.append({
                    "id": result.get("id"),
                    "distance": result.get("distance"),
                    "entity": result.get("entity", {})
                })
        
        return processed_results
    
    def _update_stats(self, success: bool, execution_time: float):
        """更新操作统计"""
        self.stats["total_operations"] += 1
        self.stats["total_execution_time"] += execution_time
        
        if success:
            self.stats["successful_operations"] += 1
        else:
            self.stats["failed_operations"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取操作统计"""
        stats = self.stats.copy()
        if stats["total_operations"] > 0:
            stats["average_execution_time"] = stats["total_execution_time"] / stats["total_operations"]
            stats["success_rate"] = stats["successful_operations"] / stats["total_operations"]
        else:
            stats["average_execution_time"] = 0.0
            stats["success_rate"] = 0.0
        
        return stats
