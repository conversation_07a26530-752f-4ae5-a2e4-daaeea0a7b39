#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus客户端池管理器

提供Milvus的统一客户端池管理功能：
- 客户端池管理和复用
- 自动重连和故障恢复
- 负载均衡
- 连接监控和统计
- 高并发支持

作者: User-DF Team
版本: 2.0.0
"""

import threading
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from pymilvus import MilvusClient, connections
from pymilvus.exceptions import MilvusException as PyMilvusException
import logging

from ...core import ConfigManager, Logger, ExceptionHandler, MilvusException, ErrorCode


@dataclass
class ClientInfo:
    """客户端信息"""
    client: MilvusClient
    created_at: float
    last_used: float
    use_count: int = 0
    is_healthy: bool = True
    connection_id: str = ""


@dataclass
class PoolStats:
    """连接池统计信息"""
    total_clients: int = 0
    active_clients: int = 0
    idle_clients: int = 0
    failed_clients: int = 0
    total_requests: int = 0
    failed_requests: int = 0


class MilvusPool:
    """Milvus客户端池管理器"""
    
    _instances: Dict[str, 'MilvusPool'] = {}
    _lock = threading.Lock()
    
    def __new__(cls, config_name: str = "milvus", **kwargs):
        """单例模式，每个配置名称对应一个实例"""
        if config_name not in cls._instances:
            with cls._lock:
                if config_name not in cls._instances:
                    instance = super().__new__(cls)
                    cls._instances[config_name] = instance
        return cls._instances[config_name]
    
    def __init__(self, config_name: str = "milvus", config_manager: Optional[ConfigManager] = None):
        """
        初始化Milvus客户端池
        
        Args:
            config_name: 配置名称
            config_manager: 配置管理器实例
        """
        if hasattr(self, '_initialized'):
            return
            
        self.config_name = config_name
        self.config_manager = config_manager or ConfigManager()
        self.logger = Logger.get_logger("MilvusPool")
        
        # 加载配置
        self._load_config()
        
        # 客户端池管理
        self._clients: Dict[str, ClientInfo] = {}
        self._client_lock = threading.RLock()
        self._stats = PoolStats()
        
        # 健康检查
        self._health_check_interval = 60  # 秒
        self._last_health_check = 0
        
        # 初始化客户端池
        self._initialize_pool()
        
        self._initialized = True
        self.logger.info(f"Milvus客户端池初始化完成: {self.uri}")
    
    def _load_config(self):
        """加载配置"""
        try:
            config = self.config_manager.get_config(self.config_name, default={})

            # 检查配置是否为空
            if config is None:
                self.logger.warning(f"未找到 {self.config_name} 配置，使用默认值")
                config = {}

            self.logger.debug(f"加载的配置: {config}")

            # 连接配置
            connection_config = config.get("connection", {})
            self.uri = connection_config.get("uri", "http://localhost:19530")
            self.token = connection_config.get("token")
            self.database = connection_config.get("database", "default")

            # 连接池配置
            pool_config = connection_config.get("pool", {})
            self.max_connections = pool_config.get("max_connections", 50)
            self.min_connections = pool_config.get("min_connections", 5)
            self.timeout = pool_config.get("timeout", 30)
            self.max_retries = pool_config.get("max_retries", 5)
            self.retry_delay = pool_config.get("retry_delay", 2.0)

            # 集合配置
            collections_config = config.get("collections", {})
            self.content_collection = collections_config.get("content_collection", "content_tower_collection")
            self.user_collection = collections_config.get("user_collection", "user_tower_collection")

            self.logger.info(f"Milvus配置加载成功: URI={self.uri}, DB={self.database}")

        except Exception as e:
            self.logger.error(f"加载Milvus配置失败: {e}")
            raise MilvusException(
                f"Milvus配置加载失败: {e}",
                ErrorCode.MILVUS_CONNECTION_ERROR
            )
    
    def _initialize_pool(self):
        """初始化客户端池"""
        try:
            # 创建最小数量的客户端
            successful_clients = 0
            for i in range(self.min_connections):
                client_id = f"client_{i}"
                try:
                    self._create_client(client_id)
                    successful_clients += 1
                except Exception as client_error:
                    self.logger.warning(f"创建客户端 {client_id} 失败: {client_error}")
                    # 继续尝试创建其他客户端，不中断整个初始化过程
                    continue
            
            if successful_clients > 0:
                self.logger.info(f"Milvus客户端池初始化完成，成功创建了 {successful_clients}/{self.min_connections} 个客户端")
            else:
                self.logger.warning("Milvus客户端池初始化失败，无法创建任何客户端，将在后续使用时重试")
            
        except Exception as e:
            self.logger.warning(f"初始化Milvus客户端池时发生错误: {e}，将在后续使用时重试")
            # 不抛出异常，允许服务继续运行
    
    def _create_client(self, client_id: str) -> ClientInfo:
        """创建新客户端"""
        try:
            # 构建连接参数
            client_params = {
                "uri": self.uri,
                "timeout": self.timeout
            }
            
            if self.token:
                client_params["token"] = self.token
            
            if self.database and self.database != "default":
                client_params["db_name"] = self.database
            
            # 创建客户端
            client = MilvusClient(**client_params)
            
            # 测试连接
            try:
                # 尝试列出集合来测试连接
                client.list_collections()
            except Exception as e:
                self.logger.warning(f"客户端连接测试失败: {e}")
                # 不抛出异常，允许客户端在后续使用时重试
            
            # 创建客户端信息
            client_info = ClientInfo(
                client=client,
                created_at=time.time(),
                last_used=time.time(),
                connection_id=client_id
            )
            
            with self._client_lock:
                self._clients[client_id] = client_info
                self._stats.total_clients += 1
            
            self.logger.debug(f"创建Milvus客户端成功: {client_id}")
            return client_info
            
        except Exception as e:
            self.logger.error(f"创建Milvus客户端失败: {e}")
            with self._client_lock:
                self._stats.failed_clients += 1
            raise MilvusException(
                f"创建Milvus客户端失败: {e}",
                ErrorCode.MILVUS_CONNECTION_ERROR
            )
    
    def get_client(self, client_id: Optional[str] = None) -> ClientInfo:
        """
        获取Milvus客户端
        
        Args:
            client_id: 指定客户端ID，如果不指定则自动选择
            
        Returns:
            客户端信息
        """
        with self._client_lock:
            self._stats.total_requests += 1
            
            # 健康检查
            self._check_clients_health()
            
            # 如果指定了客户端ID
            if client_id and client_id in self._clients:
                client_info = self._clients[client_id]
                if client_info.is_healthy:
                    client_info.last_used = time.time()
                    client_info.use_count += 1
                    self._stats.active_clients += 1
                    return client_info
            
            # 查找可用客户端（负载均衡：选择使用次数最少的）
            available_clients = [
                (client_id, client_info) for client_id, client_info in self._clients.items()
                if client_info.is_healthy
            ]
            
            if available_clients:
                # 按使用次数排序，选择使用次数最少的
                available_clients.sort(key=lambda x: x[1].use_count)
                client_id, client_info = available_clients[0]
                
                client_info.last_used = time.time()
                client_info.use_count += 1
                self._stats.active_clients += 1
                return client_info
            
            # 如果没有可用客户端且未达到最大连接数，尝试创建新客户端
            if len(self._clients) < self.max_connections:
                new_client_id = f"client_{len(self._clients)}"
                try:
                    return self._create_client(new_client_id)
                except Exception as e:
                    self.logger.warning(f"创建新客户端失败: {e}")
                    # 创建失败，继续下面的逻辑
            
            # 无法获取客户端，返回None而不是抛出异常
            self._stats.failed_requests += 1
            self.logger.warning("无法获取Milvus客户端，Milvus服务可能不可用")
            return None
    
    def _check_clients_health(self):
        """检查客户端健康状态"""
        current_time = time.time()
        
        # 限制健康检查频率
        if current_time - self._last_health_check < self._health_check_interval:
            return
        
        self._last_health_check = current_time
        
        unhealthy_clients = []
        
        for client_id, client_info in self._clients.items():
            try:
                # 执行简单操作测试连接
                client_info.client.list_collections()
                client_info.is_healthy = True
            except Exception as e:
                self.logger.warning(f"客户端 {client_id} 健康检查失败: {e}")
                client_info.is_healthy = False
                unhealthy_clients.append(client_id)
        
        # 移除不健康的客户端
        for client_id in unhealthy_clients:
            self._remove_client(client_id)
    
    def _remove_client(self, client_id: str):
        """移除客户端"""
        if client_id in self._clients:
            try:
                client_info = self._clients[client_id]
                # Milvus客户端通常不需要显式关闭
                # 但可以在这里添加清理逻辑
            except Exception as e:
                self.logger.warning(f"关闭客户端 {client_id} 时发生错误: {e}")
            
            del self._clients[client_id]
            self._stats.total_clients -= 1
            self.logger.info(f"移除不健康的客户端: {client_id}")
    
    def close_all_clients(self):
        """关闭所有客户端"""
        with self._client_lock:
            for client_id, client_info in self._clients.items():
                try:
                    # Milvus客户端通常不需要显式关闭
                    pass
                except Exception as e:
                    self.logger.warning(f"关闭客户端 {client_id} 时发生错误: {e}")
            
            self._clients.clear()
            self._stats = PoolStats()
            self.logger.info("所有Milvus客户端已关闭")
    
    def get_stats(self) -> PoolStats:
        """获取客户端池统计信息"""
        with self._client_lock:
            self._stats.idle_clients = len([
                client for client in self._clients.values() 
                if client.is_healthy and time.time() - client.last_used > 60
            ])
            return self._stats
    
    def health_check(self) -> Dict[str, Any]:
        """执行健康检查"""
        try:
            client_info = self.get_client()
            if client_info is None:
                return {
                    "status": "unhealthy",
                    "error": "无法获取Milvus客户端",
                    "uri": self.uri,
                    "database": self.database,
                    "total_clients": len(self._clients),
                    "healthy_clients": 0
                }
            
            client_info.client.list_collections()
            
            return {
                "status": "healthy",
                "uri": self.uri,
                "database": self.database,
                "total_clients": len(self._clients),
                "healthy_clients": len([c for c in self._clients.values() if c.is_healthy])
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "uri": self.uri,
                "database": self.database,
                "total_clients": len(self._clients),
                "healthy_clients": 0
            }
    
    def __del__(self):
        """析构函数"""
        try:
            self.close_all_clients()
        except:
            pass
