# 向量存储微服务 - 开发环境配置
# 版本: 1.0.0

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "1.0.0"
  environment: "development"

# ==================== 服务配置 ====================
service:
  name: "vector_writer_service"
  port: 8005

# ==================== Milvus配置 ====================
milvus:
  # 连接配置
  connection:
    uri: "http://localhost:19530"
    token: ""
    database: "default"
    
  # 连接池配置
  pool:
    max_connections: 20
    connection_timeout: 30
    
  # 集合配置
  user_collection: "user_tower_collection"
  
  # 插入配置
  insert:
    batch_size: 1000
    timeout: 30
    consistency_level: "Eventually"

# ==================== Redis配置 ====================
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""

  # 队列配置（保留兼容性）
  storage_queue_name: "vector_storage_queue"

  # Redis Stream配置
  stream:
    # Stream名称
    stream_name: "vector_storage_stream_dev"
    # 消费者组配置
    consumer_group:
      group_name: "vector_writers_dev"
      consumer_name: "writer_dev_001"
      start_id: "0"
      block_time: 1000
      count: 5
      pending_timeout: 180
      max_delivery_count: 2

# ==================== 批处理配置 ====================
batch_processing:
  # 批次大小（从队列获取的任务数）
  batch_size: 10
  # 向量插入批次大小
  vector_batch_size: 1000
  # 处理超时时间（秒）
  processing_timeout: 60
  # 批次处理间隔（毫秒）
  batch_process_interval: 100

# ==================== 日志配置 ====================
logging:
  level: DEBUG
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: logs/user_vector_service/vector_writer_service/vector_writer_service.log
  file_max_size: "50MB"
  file_backup_count: 5
  console_enabled: true
  console_colored: true
  structured: false

# ==================== 监控配置 ====================
monitoring:
  # 是否启用监控
  enabled: true
  # 统计报告间隔（秒）
  stats_report_interval: 30
  # 性能监控间隔（秒）
  performance_monitor_interval: 60
