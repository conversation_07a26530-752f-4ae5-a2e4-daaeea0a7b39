# 用户向量服务 - 开发环境全局配置文件
# 版本: 2.0.0
# 包含各个微服务的配置文件路径

# ==================== 全局配置 ====================
project:
  name: "User-DF"
  version: "2.0.0"
  environment: "development"

# ==================== 环境配置 ====================
environment:
  # 项目根目录
  project_root: "/workdir/RenL/User-DF/"

# ==================== 日志配置 ====================
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file_enabled: true
  file_path: "logs/user_vector_service/user_vector_service.log"
  file_max_size: "100MB"
  file_backup_count: 10
  console_enabled: true
  console_colored: true
  structured: false

# ==================== 微服务配置文件路径 ====================
services:
  # MongoDB读取服务配置文件路径
  mongodb_reader_service:
    config_file: "configs/user_vector_service/mongodb_reader_service/development.yaml"

  # 向量处理服务配置文件路径
  vector_processor_service:
    config_file: "configs/user_vector_service/vector_processor_service/development.yaml"

  # 向量写入服务配置文件路径
  vector_writer_service:
    config_file: "configs/user_vector_service/vector_writer_service/development.yaml"

  # 监控服务配置文件路径（如果需要）
  user_vector_monitoring_service:
    config_file: "configs/user_vector_service/user_vector_monitoring_service/development.yaml"