# User-DF 项目统一依赖文件
# 版本: 2.0.0
# 重构后的统一依赖管理

# ==================== 核心依赖 ====================
# Python版本要求: >=3.8

# 配置和日志管理
PyYAML>=6.0
coloredlogs>=15.0
python-dotenv>=1.0.0

# 数据验证和类型检查
pydantic>=1.10.0
typeguard>=2.13.0

# ==================== 数据库依赖 ====================
# MongoDB连接和数据操作
pymongo>=4.3.0
motor>=3.1.0

# Milvus向量数据库客户端
pymilvus>=2.5.0
requests>=2.28.0

# Redis连接和消息队列
redis>=4.5.0
hiredis>=2.2.0

# ==================== 数据处理依赖 ====================
# 数值计算和数据处理
numpy>=1.21.0
pandas>=1.5.0
scipy>=1.9.0

# 机器学习和向量处理
scikit-learn>=1.1.0

# 大数据处理
pyarrow>=10.0.0

# ==================== 网络和API依赖 ====================
# HTTP客户端
requests>=2.28.0
httpx>=0.24.0
aiohttp>=3.8.0

# ==================== 系统监控依赖 ====================
# 系统性能监控
psutil>=5.9.0
memory-profiler>=0.60.0

# ==================== 时间处理依赖 ====================
# 日期时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# ==================== 任务调度依赖 ====================
# 任务调度
schedule>=1.2.0

# ==================== 开发工具依赖 ====================
# 代码格式化和检查
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0
isort>=5.10.0

# ==================== 测试依赖 ====================
# 测试框架
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.8.0
pytest-xdist>=2.5.0

# 测试工具
factory-boy>=3.2.0
faker>=15.0.0

# ==================== 文档生成依赖 ====================
# 文档生成工具
sphinx>=5.0.0
sphinx-rtd-theme>=1.2.0
mkdocs>=1.4.0
mkdocs-material>=8.5.0

# ==================== 可选依赖 ====================
# 高级机器学习算法（可选）
# umap-learn>=0.5.0
# scikit-learn-extra>=0.2.0

# 分布式计算（可选）
# dask>=2022.8.0
# ray>=2.0.0

# 深度学习（可选）
# torch>=1.12.0
# tensorflow>=2.9.0

# 图数据库（可选）
# neo4j>=5.0.0

# 消息队列（可选）
# celery>=5.2.0
# kombu>=5.2.0

# 监控和追踪（可选）
# prometheus-client>=0.14.0
# jaeger-client>=4.8.0
# opentelemetry-api>=1.12.0