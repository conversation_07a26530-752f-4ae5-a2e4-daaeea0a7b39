#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务包

包含重构后的微服务架构：
- mongodb_writer_service: MongoDB写入微服务
- monitoring_service: 监控服务
- orc_processor_service: ORC数据处理微服务

作者: User-DF Team
版本: 2.0.0
"""

__version__ = "2.0.0"
__author__ = "User-DF Team"

# 导入微服务模块
from . import mongodb_writer_service
from . import monitoring_service
from . import orc_processor_service

__all__ = [
    "mongodb_writer_service",
    "monitoring_service",
    "orc_processor_service"
]
