# ORC MongoDB服务使用说明

## 概述

ORC MongoDB服务是一个高性能的数据处理服务，用于读取ORC格式的用户行为数据，通过Milvus进行PID验证，并将处理后的数据存储到MongoDB中。该服务采用微服务架构，具有自动队列控制、监控和故障恢复功能。

### 主要特性

- 🚀 **统一服务入口** - 一键启动所有相关服务
- 🔄 **自动队列控制** - 智能监控Redis队列长度，防止内存溢出
- 📊 **集成监控** - 实时监控服务状态和性能指标
- ⚙️ **灵活配置** - 支持开发/生产环境配置
- 🛡️ **故障恢复** - 自动重试和错误处理机制
- 📈 **高性能** - 批量处理和并发优化

## 快速开始

### 环境要求

- Python 3.8+
- Redis 服务器
- MongoDB 服务器
- Milvus 服务器（可选，可在配置中禁用）

### 基本使用

```bash
# 进入项目根目录
cd /path/to/User-DF

# 处理ORC数据（使用默认配置）
python3 services/orc_mongodb_service/run.py

# 处理指定日期范围的数据
python3 services/orc_mongodb_service/run.py --start-date 20250629 --end-date 20250630

# 处理指定省份的数据
python3 services/orc_mongodb_service/run.py --province-ids 100 200 210

# 使用生产环境配置
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/production.yaml
```

### 监控和状态检查

```bash
# 运行实时监控模式
python3 services/orc_mongodb_service/run.py --monitor

# 检查服务状态
python3 services/orc_mongodb_service/run.py --status

# 独立状态检查工具
python3 services/orc_mongodb_service/status.py
```

## 服务架构

### 核心组件

```
ORC MongoDB主服务
├── MongoDB写入服务 (自动启动)
│   ├── 端口: 8002
│   ├── 功能: 消费Redis队列，批量写入MongoDB
│   └── 特性: 自动重试、批量优化
├── 监控服务 (集成)
│   ├── 功能: 实时监控服务状态
│   ├── 队列监控: Redis队列长度监控
│   └── 性能统计: 处理速度、成功率等
└── ORC处理器 (集成)
    ├── 功能: 读取ORC文件，处理用户数据
    ├── Milvus集成: PID验证和过滤
    └── 队列发送: 将处理结果发送到Redis
```

### 数据流程

```
ORC文件 → ORC处理器 → Milvus验证 → Redis队列 → MongoDB写入服务 → MongoDB存储
    ↓           ↓           ↓           ↓              ↓
  文件扫描    批量处理    PID过滤    队列控制      批量写入
```

## 配置说明

### 配置文件结构

```
configs/orc_mongodb_service/
├── development.yaml        # 开发环境配置
├── production.yaml         # 生产环境配置
└── orc_mongodb_service.yaml # 生产环境配置（备选）
```

### 主要配置项

#### ORC处理配置
```yaml
orc_processor:
  start_date: "20250717"              # 处理开始日期
  end_date: "20250717"                # 处理结束日期
  province_ids: [100, 200]            # 省份ID列表
  orc_base_path: "/path/to/orc/files" # ORC文件路径
  max_pids_per_user: 300              # 每用户最大PID数
  
  batch_processing:
    batch_size: 1000                  # 用户批次大小
    pid_query_batch_size: 15000       # PID查询批次大小
```

#### Redis队列控制（核心功能）
```yaml
redis:
  host: "localhost"
  port: 6379
  queue_name: "mongodb_write_queue"
  
  queue_control:
    check_interval: 10                # 检查间隔（秒）
    pause_threshold: 150              # 暂停阈值
    resume_threshold: 20              # 恢复阈值
```

#### MongoDB配置
```yaml
mongodb:
  connection:
    host: "localhost"
    port: 27017
    database: "nrdc"
  collection: "user_pid_records_optimized"
  
  bulk_operations:
    batch_size: 1000                  # 批量操作大小
```

#### Milvus配置
```yaml
milvus:
  connection:
    uri: "http://localhost:19530"
    database: "default"
  collections:
    content_collection: "content_tower_collection_20250616"

# 是否启用Milvus PID过滤
enable_milvus_filtering: true
```

## 命令行参数

### 基本参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--start-date` | 开始日期 (YYYYMMDD) | `--start-date 20250629` |
| `--end-date` | 结束日期 (YYYYMMDD) | `--end-date 20250630` |
| `--province-ids` | 省份ID列表 | `--province-ids 100 200 210` |
| `--config` | 配置文件路径 | `--config configs/orc_mongodb_service/production.yaml` |

### 运行模式

| 参数 | 说明 |
|------|------|
| `--monitor` | 运行实时监控模式 |
| `--status` | 显示服务状态 |

### 使用示例

```bash
# 处理昨天的数据
python3 services/orc_mongodb_service/run.py --start-date $(date -d yesterday +%Y%m%d) --end-date $(date -d yesterday +%Y%m%d)

# 处理特定省份的本月数据
python3 services/orc_mongodb_service/run.py --start-date 20250701 --end-date 20250731 --province-ids 100 200

# 使用生产环境配置处理数据
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/production.yaml --start-date 20250629
```

## 核心功能详解

### 1. 自动队列控制

服务会自动监控Redis队列长度，当队列积压过多时会暂停ORC处理，避免内存溢出：

- **暂停机制**: 当队列长度超过 `pause_threshold` 时，暂停ORC文件处理
- **恢复机制**: 当队列长度降至 `resume_threshold` 以下时，自动恢复处理
- **智能等待**: 在暂停期间定期检查队列状态，无需人工干预

```yaml
# 队列控制配置示例
redis:
  queue_control:
    check_interval: 10        # 每10秒检查一次队列长度
    pause_threshold: 150      # 队列长度超过150时暂停
    resume_threshold: 20      # 队列长度低于20时恢复
```

### 2. 批量处理优化

- **用户批量处理**: 每次处理1000个用户（可配置）
- **PID批量查询**: 对Milvus进行批量查询，提高效率
- **MongoDB批量写入**: 批量插入/更新操作，减少数据库压力

### 3. 数据处理流程

1. **文件扫描**: 根据日期和省份扫描ORC文件
2. **数据读取**: 批量读取ORC文件中的用户数据
3. **PID验证**: 通过Milvus验证PID的有效性（可选）
4. **数据清洗**: 去重、排序、限制PID数量
5. **队列发送**: 将处理结果发送到Redis队列
6. **MongoDB存储**: 后台服务消费队列并写入MongoDB

### 4. 监控功能

实时监控包括：
- **服务状态**: 各个子服务的运行状态
- **队列监控**: Redis队列长度和处理速度
- **性能指标**: 处理速度、成功率、错误统计
- **资源使用**: 内存、CPU使用情况

## 部署指南

### 开发环境部署

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **启动依赖服务**
```bash
# 启动Redis
redis-server

# 启动MongoDB
mongod --dbpath /path/to/mongodb/data

# 启动Milvus（可选）
docker run -p 19530:19530 milvusdb/milvus:latest
```

3. **配置文件**
```bash
# 复制并修改开发环境配置
cp configs/orc_mongodb_service/development.yaml configs/orc_mongodb_service/my_dev.yaml
# 根据实际环境修改配置
```

4. **运行测试**
```bash
# 运行测试验证配置
python3 services/orc_mongodb_service/test_service.py

# 检查服务状态
python3 services/orc_mongodb_service/status.py
```

### 生产环境部署

1. **环境准备**
- 确保Redis、MongoDB、Milvus服务稳定运行
- 配置适当的资源限制和监控
- 设置日志轮转和备份策略

2. **配置优化**
```yaml
# 生产环境推荐配置
orc_processor:
  batch_processing:
    batch_size: 1000
    pid_query_batch_size: 30000
  performance:
    concurrency:
      max_concurrent_files: 10
      max_concurrent_batches: 20

redis:
  queue_control:
    pause_threshold: 150
    resume_threshold: 20

mongodb:
  bulk_operations:
    batch_size: 1000
```

3. **定时任务设置**
```bash
# 添加到crontab
# 每天凌晨2点处理前一天的数据
0 2 * * * cd /path/to/User-DF && python3 services/orc_mongodb_service/run.py --start-date $(date -d yesterday +\%Y\%m\%d) --end-date $(date -d yesterday +\%Y\%m\%d) --config configs/orc_mongodb_service/production.yaml
```

## 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查配置文件
python3 services/orc_mongodb_service/test_service.py

# 检查依赖服务
python3 services/orc_mongodb_service/status.py

# 查看详细日志
tail -f logs/orc_mongodb_service/orc_mongodb_service.log
```

#### 2. 队列积压
```bash
# 检查队列状态
python3 services/orc_mongodb_service/status.py

# 调整队列控制参数
# 在配置文件中降低pause_threshold或增加resume_threshold
```

#### 3. MongoDB连接问题
```bash
# 测试MongoDB连接
python3 -c "
from pymongo import MongoClient
client = MongoClient('mongodb://localhost:27017/')
print(client.admin.command('ping'))
"
```

#### 4. Milvus连接问题
```bash
# 可以临时禁用Milvus过滤
# 在配置文件中设置: enable_milvus_filtering: false
```

### 性能调优

#### 1. 批处理大小调优
- **小文件多**: 减少 `batch_size`，增加并发数
- **大文件少**: 增加 `batch_size`，减少并发数
- **内存限制**: 根据可用内存调整 `pid_query_batch_size`

#### 2. 队列控制调优
- **高吞吐**: 增加 `pause_threshold`
- **低延迟**: 减少 `pause_threshold`
- **稳定性优先**: 增大阈值差值（pause_threshold - resume_threshold）

#### 3. 数据库优化
- **MongoDB**: 创建适当的索引，调整写入关注点
- **Redis**: 配置适当的内存策略和持久化
- **Milvus**: 优化向量索引参数

## 监控和告警

### 日志监控
```bash
# 实时查看日志
tail -f logs/orc_mongodb_service/orc_mongodb_service.log

# 搜索错误日志
grep -i error logs/orc_mongodb_service/orc_mongodb_service.log

# 统计处理量
grep "处理完成" logs/orc_mongodb_service/orc_mongodb_service.log | wc -l
```

### 性能监控
```bash
# 运行监控模式
python3 services/orc_mongodb_service/run.py --monitor

# 定期状态检查
watch -n 30 "python3 services/orc_mongodb_service/status.py"
```

### 告警设置
建议监控以下指标：
- 队列长度超过阈值
- 服务异常退出
- 处理速度异常下降
- 错误率超过阈值

## API参考

### 主服务类 (ORCMongoDBMainService)

```python
from services.orc_mongodb_service.main import ORCMongoDBMainService

# 创建服务实例
service = ORCMongoDBMainService("configs/orc_mongodb_service/development.yaml")

# 初始化服务
await service.initialize()

# 处理ORC数据
await service.process_orc_data(
    start_date="20250629",
    end_date="20250630",
    province_ids=[100, 200]
)

# 获取服务状态
status = await service.get_service_status()

# 关闭服务
await service.shutdown()
```

### 配置管理

```python
from shared.core import ConfigManager

# 加载配置
config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
config = config_manager.get_config("orc_mongodb_service")

# 获取特定配置项
batch_size = config_manager.get_config("orc_mongodb_service", "orc_processor.batch_processing.batch_size")
```

## 版本历史

- **v3.0.0** - 重构版本，统一服务入口，简化配置
- **v2.0.0** - 微服务架构，分离各个组件
- **v1.0.0** - 初始版本

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
