# ORC MongoDB服务使用总结

## 📋 服务概述

ORC MongoDB服务是一个重构后的统一数据处理服务，具有以下特点：

- **版本**: 3.0.0 (重构版本)
- **架构**: 统一服务入口 + 自动子服务管理
- **核心功能**: ORC数据处理 + Redis队列控制 + MongoDB存储
- **特色**: 自动队列长度控制，防止内存溢出

## 🚀 快速开始

### 1. 最简单的使用方式
```bash
# 进入项目目录
cd /path/to/User-DF

# 处理ORC数据（使用默认配置）
python3 services/orc_mongodb_service/run.py
```

### 2. 常用命令
```bash
# 处理指定日期范围
python3 services/orc_mongodb_service/run.py --start-date 20250629 --end-date 20250630

# 处理指定省份
python3 services/orc_mongodb_service/run.py --province-ids 100 200 210

# 实时监控模式
python3 services/orc_mongodb_service/run.py --monitor

# 检查服务状态
python3 services/orc_mongodb_service/status.py
```

## ⚙️ 配置文件

### 配置文件位置
```
configs/orc_mongodb_service/
├── development.yaml        # 开发环境（默认）
├── production.yaml         # 生产环境
└── orc_mongodb_service.yaml # 生产环境（备选）
```

### 关键配置项
```yaml
# 队列控制（核心功能）
redis:
  queue_control:
    pause_threshold: 150    # 暂停阈值
    resume_threshold: 20    # 恢复阈值

# 批处理优化
orc_processor:
  batch_processing:
    batch_size: 1000        # 用户批次大小
    pid_query_batch_size: 15000  # PID查询批次

# 数据库连接
mongodb:
  connection:
    host: "localhost"
    database: "nrdc"
```

## 🔧 服务管理

### 服务组件
1. **主服务** (`main.py`) - 统一入口，自动管理所有子服务
2. **MongoDB写入服务** - 自动启动，端口8002
3. **监控服务** - 集成在主服务中
4. **ORC处理器** - 集成在主服务中

### 自动化特性
- ✅ 自动启动MongoDB写入服务
- ✅ 自动监控Redis队列长度
- ✅ 队列积压时自动暂停处理
- ✅ 队列恢复时自动继续处理
- ✅ 集成实时监控和状态检查

## 📊 监控和状态

### 状态检查
```bash
# 快速状态检查
python3 services/orc_mongodb_service/status.py

# 详细状态检查
python3 services/orc_mongodb_service/run.py --status

# 实时监控
python3 services/orc_mongodb_service/run.py --monitor
```

### 监控指标
- 服务运行状态
- Redis队列长度
- 处理速度和成功率
- 内存和CPU使用情况

## 🛠️ 故障排除

### 常见问题及解决方案

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 配置错误 | 服务启动失败 | 运行 `test_service.py` 检查配置 |
| 依赖服务未启动 | 连接失败 | 检查Redis/MongoDB/Milvus服务状态 |
| 队列积压 | 处理缓慢 | 检查队列控制参数，调整阈值 |
| 内存不足 | 服务崩溃 | 减少批处理大小，增加队列控制 |

### 调试命令
```bash
# 运行测试验证配置
python3 services/orc_mongodb_service/test_service.py

# 查看详细日志
tail -f logs/orc_mongodb_service/orc_mongodb_service.log

# 检查依赖服务
redis-cli ping
mongo --eval "db.runCommand('ping')"
```

## 📈 性能优化

### 开发环境设置
```yaml
batch_size: 100
pid_query_batch_size: 1000
pause_threshold: 30
resume_threshold: 10
```

### 生产环境设置
```yaml
batch_size: 1000
pid_query_batch_size: 30000
pause_threshold: 150
resume_threshold: 20
max_concurrent_files: 10
```

## 🔄 数据处理流程

```
1. ORC文件扫描 → 2. 批量数据读取 → 3. Milvus PID验证 → 4. Redis队列 → 5. MongoDB存储
   ↓                ↓                  ↓                ↓             ↓
 按日期/省份筛选    用户批量处理        PID有效性检查     队列长度控制   批量写入优化
```

## 📅 定时任务

### Crontab示例
```bash
# 每天凌晨2点处理前一天数据
0 2 * * * cd /path/to/User-DF && python3 services/orc_mongodb_service/run.py --start-date $(date -d yesterday +\%Y\%m\%d) --end-date $(date -d yesterday +\%Y\%m\%d) --config configs/orc_mongodb_service/production.yaml

# 每小时检查服务状态
0 * * * * cd /path/to/User-DF && python3 services/orc_mongodb_service/status.py >> /var/log/orc_service_status.log
```

## 📚 文档资源

- **[README.md](README.md)** - 完整使用说明和API文档
- **[QUICK_REFERENCE.md](QUICK_REFERENCE.md)** - 快速参考卡片
- **[USAGE_SUMMARY.md](USAGE_SUMMARY.md)** - 本文件，使用总结
- **[example.py](example.py)** - 代码使用示例
- **[test_service.py](test_service.py)** - 配置和功能测试

## 🎯 重要提醒

1. **队列控制是核心功能** - 防止Redis队列积压导致内存溢出
2. **统一入口简化使用** - 只需要运行 `run.py` 即可启动所有服务
3. **配置文件已简化** - 从多个分散配置合并为单一配置文件
4. **自动化程度高** - 大部分操作都是自动化的，减少人工干预
5. **监控功能集成** - 无需单独启动监控服务

## 📞 支持

如果遇到问题：
1. 首先运行 `test_service.py` 检查配置
2. 查看 `logs/orc_mongodb_service/orc_mongodb_service.log` 日志
3. 运行 `status.py` 检查服务状态
4. 参考 README.md 中的故障排除章节

---

💡 **提示**: 这是重构后的3.0.0版本，相比之前版本大幅简化了使用方式和配置管理。
