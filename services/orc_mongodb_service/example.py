#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务使用示例
演示重构后的服务如何使用
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from services.orc_mongodb_service.main import ORCMongoDBMainService


async def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    try:
        # 创建服务实例（使用开发环境配置）
        service = ORCMongoDBMainService("configs/orc_mongodb_service/development.yaml")
        
        # 初始化服务（这会自动启动MongoDB写入服务和监控服务）
        print("初始化服务...")
        await service.initialize()
        
        # 获取服务状态
        print("获取服务状态...")
        status = await service.get_service_status()
        print(f"主服务运行状态: {status['main_service']['is_running']}")
        print(f"子服务数量: {len(status['services'])}")
        
        # 模拟处理ORC数据（使用小范围测试数据）
        print("开始处理ORC数据...")
        await service.process_orc_data(
            start_date="20250717",
            end_date="20250717", 
            province_ids=[100]  # 只处理一个省份作为示例
        )
        
        print("✅ 基本使用示例完成")
        
    except Exception as e:
        print(f"❌ 基本使用示例失败: {e}")
    finally:
        # 关闭服务
        if 'service' in locals():
            await service.shutdown()


async def example_monitoring_mode():
    """监控模式示例"""
    print("\n=== 监控模式示例 ===")
    
    try:
        # 创建服务实例
        service = ORCMongoDBMainService("configs/orc_mongodb_service/development.yaml")
        
        # 初始化服务
        print("初始化监控服务...")
        await service.initialize()
        
        # 运行监控循环（运行5秒后停止）
        print("启动监控模式（5秒后自动停止）...")
        
        # 创建监控任务
        monitoring_task = asyncio.create_task(service.run_monitoring_loop())
        
        # 等待5秒后停止
        await asyncio.sleep(5)
        monitoring_task.cancel()
        
        try:
            await monitoring_task
        except asyncio.CancelledError:
            pass
        
        print("✅ 监控模式示例完成")
        
    except Exception as e:
        print(f"❌ 监控模式示例失败: {e}")
    finally:
        # 关闭服务
        if 'service' in locals():
            await service.shutdown()


async def example_status_check():
    """状态检查示例"""
    print("\n=== 状态检查示例 ===")
    
    try:
        # 创建服务实例
        service = ORCMongoDBMainService("configs/orc_mongodb_service/development.yaml")
        
        # 初始化服务
        print("初始化服务...")
        await service.initialize()
        
        # 获取详细状态
        print("获取服务状态...")
        status = await service.get_service_status()
        
        # 显示状态信息
        print("\n📊 服务状态详情:")
        print(f"主服务运行时间: {status['main_service']['uptime']:.1f}秒")
        
        for service_key, service_status in status['services'].items():
            print(f"\n🔧 {service_status['name']}:")
            print(f"  状态: {service_status['status']}")
            if service_status.get('pid'):
                print(f"  进程ID: {service_status['pid']}")
            if service_status.get('port'):
                print(f"  端口: {service_status['port']}")
            if service_status.get('error_message'):
                print(f"  错误: {service_status['error_message']}")
        
        # 显示监控统计（如果有）
        if 'monitoring_stats' in status:
            stats = status['monitoring_stats']
            print(f"\n📈 监控统计:")
            print(f"  队列状态: {stats.get('queue', {}).get('status', 'unknown')}")
            print(f"  队列长度: {stats.get('queue', {}).get('queue_length', 0)}")
        
        print("✅ 状态检查示例完成")
        
    except Exception as e:
        print(f"❌ 状态检查示例失败: {e}")
    finally:
        # 关闭服务
        if 'service' in locals():
            await service.shutdown()


async def example_configuration_demo():
    """配置演示示例"""
    print("\n=== 配置演示示例 ===")
    
    try:
        from shared.core import ConfigManager
        
        # 加载配置
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
        
        # 获取各个配置模块
        service_config = config_manager.get_config("orc_mongodb_service")
        global_config = config_manager.get_config("global")
        mongodb_config = config_manager.get_config("mongodb")
        
        print("📋 配置信息:")
        print(f"项目名称: {global_config['project']['name']}")
        print(f"项目版本: {global_config['project']['version']}")
        print(f"运行环境: {global_config['project']['environment']}")
        
        print(f"\n🔧 ORC处理配置:")
        orc_config = service_config['orc_processor']
        print(f"批次大小: {orc_config['batch_processing']['batch_size']}")
        print(f"PID查询批次: {orc_config['batch_processing']['pid_query_batch_size']}")
        print(f"每用户最大PID数: {orc_config['max_pids_per_user']}")
        
        print(f"\n🔄 队列控制配置:")
        queue_control = service_config['redis']['queue_control']
        print(f"检查间隔: {queue_control['check_interval']}秒")
        print(f"暂停阈值: {queue_control['pause_threshold']}")
        print(f"恢复阈值: {queue_control['resume_threshold']}")
        
        print(f"\n🗄️ MongoDB配置:")
        print(f"主机: {mongodb_config['connection']['host']}")
        print(f"端口: {mongodb_config['connection']['port']}")
        print(f"数据库: {mongodb_config['connection']['database']}")
        print(f"集合: {mongodb_config['collection']}")
        
        print("✅ 配置演示示例完成")
        
    except Exception as e:
        print(f"❌ 配置演示示例失败: {e}")


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 ORC MongoDB服务重构版本使用示例")
    print("=" * 60)
    
    # 运行配置演示（不需要启动服务）
    await example_configuration_demo()
    
    # 运行状态检查示例
    await example_status_check()
    
    # 注意：以下示例需要实际的服务环境，在演示中跳过
    print("\n⚠️  以下示例需要完整的服务环境（Redis、MongoDB、Milvus），在演示中跳过:")
    print("  - 基本使用示例（处理ORC数据）")
    print("  - 监控模式示例")
    
    print("\n" + "=" * 60)
    print("🎉 所有可运行的示例已完成！")
    print("\n💡 要运行完整功能，请确保以下服务正在运行:")
    print("  - Redis (localhost:6379)")
    print("  - MongoDB (localhost:27017)")
    print("  - Milvus (localhost:19530) [可选，可在配置中禁用]")
    print("\n📖 更多使用方法请参考 README.md 和 MIGRATION.md")


if __name__ == "__main__":
    asyncio.run(main())
