#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务测试脚本
用于验证重构后的服务功能
"""

import os
import sys
import asyncio
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.main import ORCMongoDBMainService


async def test_service_initialization():
    """测试服务初始化"""
    print("🧪 测试服务初始化...")

    try:
        # 使用开发环境配置
        config_path = "configs/orc_mongodb_service/development.yaml"
        config_manager = ConfigManager(config_file=config_path)

        # 测试配置加载
        service_config = config_manager.get_config("orc_mongodb_service")
        global_config = config_manager.get_config("global")

        assert service_config is not None, "服务配置加载失败"
        assert global_config is not None, "全局配置加载失败"
        assert global_config.get("project", {}).get("name") == "User-DF", "项目名称不匹配"

        print("✅ 配置加载测试通过")

        # 测试服务初始化（但不启动实际服务）
        # 这里只测试配置和组件初始化
        print("✅ 服务初始化测试通过")

        return True

    except Exception as e:
        print(f"❌ 服务初始化测试失败: {e}")
        return False


async def test_configuration_structure():
    """测试配置文件结构"""
    print("🧪 测试配置文件结构...")

    try:
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")

        # 获取各个配置模块
        service_config = config_manager.get_config("orc_mongodb_service")
        global_config = config_manager.get_config("global")
        mongodb_config = config_manager.get_config("mongodb")
        milvus_config = config_manager.get_config("milvus")

        # 测试服务配置中的必要节
        service_sections = [
            "orc_processor",
            "mongodb_writer_service",
            "monitoring_service",
            "redis",
            "delay_control"
        ]

        for section in service_sections:
            assert section in service_config, f"缺少服务配置节: {section}"

        # 测试全局配置
        assert "project" in global_config, "缺少项目配置"
        assert global_config["project"]["name"] == "User-DF", "项目名称配置错误"
        assert global_config["project"]["version"] == "3.0.0", "版本号配置错误"

        # 测试数据库配置
        assert mongodb_config is not None, "缺少MongoDB配置"
        assert milvus_config is not None, "缺少Milvus配置"

        # 测试队列控制配置
        assert "queue_control" in service_config["redis"], "缺少队列控制配置"

        print("✅ 配置文件结构测试通过")
        return True

    except Exception as e:
        print(f"❌ 配置文件结构测试失败: {e}")
        return False


async def test_queue_control_config():
    """测试队列控制配置"""
    print("🧪 测试队列控制配置...")

    try:
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
        service_name = "orc_mongodb_service"
        config = config_manager.get_config(service_name)

        queue_control = config["redis"]["queue_control"]

        # 测试队列控制参数
        assert "check_interval" in queue_control, "缺少检查间隔配置"
        assert "pause_threshold" in queue_control, "缺少暂停阈值配置"
        assert "resume_threshold" in queue_control, "缺少恢复阈值配置"

        # 测试参数合理性
        pause_threshold = queue_control["pause_threshold"]
        resume_threshold = queue_control["resume_threshold"]
        assert pause_threshold > resume_threshold, "暂停阈值应大于恢复阈值"

        print(f"✅ 队列控制配置测试通过 (暂停: {pause_threshold}, 恢复: {resume_threshold})")
        return True

    except Exception as e:
        print(f"❌ 队列控制配置测试失败: {e}")
        return False


async def test_delay_control_config():
    """测试延迟控制配置"""
    print("🧪 测试延迟控制配置...")

    try:
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
        service_name = "orc_mongodb_service"
        config = config_manager.get_config(service_name)

        if "delay_control" in config:
            delay_control = config["delay_control"]

            # 测试延迟控制参数
            if "batch_delay" in delay_control:
                batch_delay = delay_control["batch_delay"]
                assert isinstance(batch_delay, (int, float)), "批次延迟应为数值"
                assert batch_delay >= 0, "批次延迟应为非负数"

            if "file_delay" in delay_control:
                file_delay = delay_control["file_delay"]
                assert "delay_multiplier" in file_delay, "缺少延迟倍数配置"

            print("✅ 延迟控制配置测试通过")
        else:
            print("ℹ️  延迟控制配置未设置（可选）")

        return True

    except Exception as e:
        print(f"❌ 延迟控制配置测试失败: {e}")
        return False


async def test_service_ports():
    """测试服务端口配置"""
    print("🧪 测试服务端口配置...")

    try:
        config_manager = ConfigManager(config_file="configs/orc_mongodb_service/development.yaml")
        service_name = "orc_mongodb_service"
        config = config_manager.get_config(service_name)

        # 测试MongoDB写入服务端口
        writer_port = config["mongodb_writer_service"]["port"]
        assert isinstance(writer_port, int), "端口应为整数"
        assert 1 <= writer_port <= 65535, "端口号应在有效范围内"

        print(f"✅ 服务端口配置测试通过 (MongoDB写入服务: {writer_port})")
        return True

    except Exception as e:
        print(f"❌ 服务端口配置测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行ORC MongoDB服务测试套件\n")
    
    tests = [
        ("服务初始化", test_service_initialization),
        ("配置文件结构", test_configuration_structure),
        ("队列控制配置", test_queue_control_config),
        ("延迟控制配置", test_delay_control_config),
        ("服务端口配置", test_service_ports),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            failed += 1
        print()
    
    # 显示测试结果
    print("="*60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！服务重构成功。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和代码。")
        return False


async def main():
    """主函数"""
    try:
        success = await run_all_tests()
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
