#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务简化启动脚本
重构后的统一启动入口

使用方法:
  # 处理ORC数据（默认模式）
  python3 services/orc_mongodb_service/run.py
  
  # 处理指定日期范围
  python3 services/orc_mongodb_service/run.py --start-date 20250629 --end-date 20250630
  
  # 处理指定省份
  python3 services/orc_mongodb_service/run.py --province-ids 100 200 210
  
  # 运行监控模式
  python3 services/orc_mongodb_service/run.py --monitor
  
  # 显示服务状态
  python3 services/orc_mongodb_service/run.py --status
  
  # 使用生产环境配置
  python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/orc_mongodb_service.yaml
"""

import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from services.orc_mongodb_service.main import main

if __name__ == "__main__":
    asyncio.run(main())
