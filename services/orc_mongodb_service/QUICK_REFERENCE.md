# ORC MongoDB服务快速参考

## 🚀 常用命令

### 基本操作
```bash
# 处理ORC数据（默认配置）
python3 services/orc_mongodb_service/run.py

# 处理指定日期
python3 services/orc_mongodb_service/run.py --start-date 20250629 --end-date 20250630

# 处理指定省份
python3 services/orc_mongodb_service/run.py --province-ids 100 200 210

# 使用生产配置
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/production.yaml
```

### 监控和状态
```bash
# 实时监控
python3 services/orc_mongodb_service/run.py --monitor

# 检查状态
python3 services/orc_mongodb_service/run.py --status

# 独立状态检查
python3 services/orc_mongodb_service/status.py

# 运行测试
python3 services/orc_mongodb_service/test_service.py
```

## ⚙️ 核心配置

### 队列控制（防止内存溢出）
```yaml
redis:
  queue_control:
    pause_threshold: 150    # 队列长度超过此值时暂停
    resume_threshold: 20    # 队列长度低于此值时恢复
    check_interval: 10      # 检查间隔（秒）
```

### 批处理优化
```yaml
orc_processor:
  batch_processing:
    batch_size: 1000              # 用户批次大小
    pid_query_batch_size: 15000   # PID查询批次
  max_pids_per_user: 300          # 每用户最大PID数
```

### 数据库连接
```yaml
mongodb:
  connection:
    host: "localhost"
    port: 27017
    database: "nrdc"
  collection: "user_pid_records_optimized"

redis:
  host: "localhost"
  port: 6379
  queue_name: "mongodb_write_queue"

milvus:
  connection:
    uri: "http://localhost:19530"
    database: "default"
```

## 📊 服务架构

```
主服务 (main.py)
├── MongoDB写入服务 (端口8002) - 自动启动
├── 监控服务 - 集成
└── ORC处理器 - 集成

数据流: ORC文件 → 处理器 → Milvus验证 → Redis队列 → MongoDB
```

## 🔧 故障排除

### 常见问题
| 问题 | 解决方案 |
|------|----------|
| 服务启动失败 | 检查配置文件和依赖服务 |
| 队列积压 | 调整队列控制参数 |
| MongoDB连接失败 | 检查MongoDB服务状态 |
| Milvus连接失败 | 设置 `enable_milvus_filtering: false` |

### 检查命令
```bash
# 检查配置
python3 services/orc_mongodb_service/test_service.py

# 查看日志
tail -f logs/orc_mongodb_service/orc_mongodb_service.log

# 检查依赖服务
redis-cli ping                    # Redis
mongo --eval "db.runCommand('ping')"  # MongoDB
```

## 📁 文件结构

```
services/orc_mongodb_service/
├── run.py                    # 主启动脚本
├── main.py                   # 主服务类
├── status.py                 # 状态检查工具
├── test_service.py           # 测试脚本
├── example.py                # 使用示例
├── README.md                 # 详细文档
└── QUICK_REFERENCE.md        # 本文件

configs/orc_mongodb_service/
├── development.yaml          # 开发环境配置
├── production.yaml           # 生产环境配置
└── orc_mongodb_service.yaml  # 生产配置（备选）
```

## 🎯 性能调优

### 开发环境推荐
```yaml
batch_size: 100
pid_query_batch_size: 1000
pause_threshold: 30
resume_threshold: 10
```

### 生产环境推荐
```yaml
batch_size: 1000
pid_query_batch_size: 30000
pause_threshold: 150
resume_threshold: 20
max_concurrent_files: 10
```

## 📅 定时任务示例

```bash
# 每天凌晨2点处理前一天数据
0 2 * * * cd /path/to/User-DF && python3 services/orc_mongodb_service/run.py --start-date $(date -d yesterday +\%Y\%m\%d) --end-date $(date -d yesterday +\%Y\%m\%d) --config configs/orc_mongodb_service/production.yaml

# 每小时检查服务状态
0 * * * * cd /path/to/User-DF && python3 services/orc_mongodb_service/status.py
```

## 🔍 监控指标

### 关键指标
- 队列长度 (Redis)
- 处理速度 (记录/秒)
- 错误率 (%)
- 内存使用率 (%)
- 服务可用性 (%)

### 告警阈值建议
- 队列长度 > 200
- 错误率 > 5%
- 内存使用率 > 80%
- 服务不可用 > 5分钟

---

💡 **提示**: 更详细的信息请参考 [README.md](README.md)
