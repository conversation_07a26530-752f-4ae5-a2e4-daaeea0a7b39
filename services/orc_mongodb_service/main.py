#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务主入口
重构后的统一服务入口，集成了监控服务和自动启动mongodb_writer_service

功能：
1. 自动启动mongodb_writer_service
2. 集成监控服务
3. 处理ORC数据并考虑Redis队列长度
4. 统一的配置管理
"""

import os
import sys
import asyncio
import argparse
import signal
import subprocess
import time
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.orc_processor_service.service import ORCProcessor
from services.orc_mongodb_service.monitoring_service.monitor import MonitoringService


@dataclass
class ServiceStatus:
    """服务状态"""
    name: str
    status: str
    pid: Optional[int] = None
    port: Optional[int] = None
    uptime: float = 0.0
    error_message: Optional[str] = None


class ORCMongoDBMainService:
    """ORC MongoDB主服务"""
    
    def __init__(self, config_path: Optional[str] = None):
        # 配置管理
        self.config_path = config_path or "configs/orc_mongodb_service/development.yaml"
        self.config_manager = ConfigManager(config_file=self.config_path)
        # 从配置文件路径推断服务名称
        self.service_name = "orc_mongodb_service"
        self.config = self.config_manager.get_config(self.service_name)
        self.logger = Logger.get_logger(__name__)
        
        # 服务状态
        self.is_running = False
        self.shutdown_requested = False
        
        # 子服务
        self.mongodb_writer_process = None
        self.monitoring_service = None
        self.orc_processor = None
        
        # 服务状态跟踪
        self.services_status = {}
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备停止服务...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("=== 初始化ORC MongoDB服务 ===")
            
            # 设置信号处理器
            self.setup_signal_handlers()
            
            # 启动MongoDB写入服务
            await self._start_mongodb_writer_service()
            
            # 初始化监控服务
            await self._initialize_monitoring_service()
            
            # 初始化ORC处理器
            await self._initialize_orc_processor()
            
            self.is_running = True
            self.logger.info("=== ORC MongoDB服务初始化完成 ===")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            await self.shutdown()
            raise
    
    async def _start_mongodb_writer_service(self):
        """启动MongoDB写入服务"""
        try:
            self.logger.info("启动MongoDB写入服务...")
            
            # 获取MongoDB写入服务配置
            writer_config = self.config.get("mongodb_writer_service", {})
            port = writer_config.get("port", 8002)
            host = writer_config.get("host", "0.0.0.0")
            
            # 构建启动命令
            cmd = [
                sys.executable, "-m", 
                "services.orc_mongodb_service.mongodb_writer_service.main",
                "--host", host,
                "--port", str(port),
                "--config", self.config_path
            ]
            
            # 设置环境变量
            env = os.environ.copy()
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            current_pythonpath = env.get('PYTHONPATH', '')
            if project_root not in current_pythonpath:
                env['PYTHONPATH'] = f"{project_root}:{current_pythonpath}" if current_pythonpath else project_root
            
            # 启动进程
            self.mongodb_writer_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # 等待服务启动
            await self._wait_for_service_ready(f"http://localhost:{port}/health", "MongoDB写入服务")
            
            self.services_status["mongodb_writer"] = ServiceStatus(
                name="MongoDB写入服务",
                status="running",
                pid=self.mongodb_writer_process.pid,
                port=port
            )
            
            self.logger.info(f"MongoDB写入服务启动成功 (PID: {self.mongodb_writer_process.pid}, 端口: {port})")
            
        except Exception as e:
            self.logger.error(f"启动MongoDB写入服务失败: {e}")
            raise
    
    async def _wait_for_service_ready(self, health_url: str, service_name: str, timeout: int = 30):
        """等待服务就绪"""
        import aiohttp
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(health_url, timeout=aiohttp.ClientTimeout(total=2)) as response:
                        if response.status == 200:
                            self.logger.info(f"{service_name}已就绪")
                            return
            except:
                pass
            
            await asyncio.sleep(1)
        
        raise Exception(f"等待{service_name}就绪超时")
    
    async def _initialize_monitoring_service(self):
        """初始化监控服务"""
        try:
            self.logger.info("初始化监控服务...")
            
            self.monitoring_service = MonitoringService(self.config_manager)
            await self.monitoring_service.initialize()
            
            self.services_status["monitoring"] = ServiceStatus(
                name="监控服务",
                status="running"
            )
            
            self.logger.info("监控服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"监控服务初始化失败: {e}")
            raise
    
    async def _initialize_orc_processor(self):
        """初始化ORC处理器"""
        try:
            self.logger.info("初始化ORC处理器...")
            
            self.orc_processor = ORCProcessor(self.config_manager)
            await self.orc_processor.initialize()
            
            self.services_status["orc_processor"] = ServiceStatus(
                name="ORC处理器",
                status="initialized"
            )
            
            self.logger.info("ORC处理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"ORC处理器初始化失败: {e}")
            raise
    
    async def process_orc_data(self, start_date: Optional[str] = None,
                              end_date: Optional[str] = None,
                              province_ids: Optional[List[int]] = None):
        """处理ORC数据"""
        try:
            self.logger.info("=== 开始处理ORC数据 ===")

            # 更新ORC处理器状态
            self.services_status["orc_processor"].status = "processing"

            # 应用延迟控制配置
            await self._apply_delay_control()

            # 开始处理
            await self.orc_processor.process_files(
                start_date=start_date,
                end_date=end_date,
                province_ids=province_ids
            )

            # 更新状态
            self.services_status["orc_processor"].status = "completed"

            self.logger.info("=== ORC数据处理完成 ===")

        except Exception as e:
            self.services_status["orc_processor"].status = "error"
            self.services_status["orc_processor"].error_message = str(e)
            self.logger.error(f"ORC数据处理失败: {e}")
            raise

    async def _apply_delay_control(self):
        """应用延迟控制配置"""
        try:
            delay_config = self.config.get("delay_control", {})
            batch_delay = delay_config.get("batch_delay", 0) / 1000.0  # 转换为秒

            if batch_delay > 0:
                self.logger.info(f"应用批次延迟控制: {batch_delay * 1000:.0f}ms")
                # 这里可以将延迟配置传递给ORC处理器
                # 实际的延迟控制在ORC处理器内部实现

        except Exception as e:
            self.logger.warning(f"应用延迟控制失败: {e}")
    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "main_service": {
                "is_running": self.is_running,
                "uptime": time.time() - getattr(self, 'start_time', time.time())
            },
            "services": {}
        }
        
        for service_key, service_status in self.services_status.items():
            status["services"][service_key] = asdict(service_status)
        
        # 获取监控服务的详细状态
        if self.monitoring_service:
            try:
                monitoring_stats = await self.monitoring_service.collect_all_stats()
                status["monitoring_stats"] = monitoring_stats
            except Exception as e:
                self.logger.warning(f"获取监控统计失败: {e}")
        
        return status
    
    async def run_monitoring_loop(self):
        """运行监控循环"""
        if self.monitoring_service:
            await self.monitoring_service.run_monitoring_loop()
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("=== 关闭ORC MongoDB服务 ===")
            self.is_running = False
            
            # 关闭ORC处理器
            if self.orc_processor:
                await self.orc_processor.shutdown()
            
            # 关闭监控服务
            if self.monitoring_service:
                await self.monitoring_service.shutdown()
            
            # 关闭MongoDB写入服务
            if self.mongodb_writer_process:
                self.mongodb_writer_process.terminate()
                try:
                    self.mongodb_writer_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.mongodb_writer_process.kill()
                    self.mongodb_writer_process.wait()
                self.logger.info("MongoDB写入服务已关闭")
            
            self.logger.info("=== ORC MongoDB服务已关闭 ===")
            
        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="ORC MongoDB服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理ORC数据
  python3 services/orc_mongodb_service/main.py --start-date 20250629 --end-date 20250630
  
  # 处理指定省份
  python3 services/orc_mongodb_service/main.py --province-ids 100 200 210
  
  # 运行监控模式
  python3 services/orc_mongodb_service/main.py --monitor
  
  # 指定配置文件
  python3 services/orc_mongodb_service/main.py --config configs/orc_mongodb_service/production.yaml
        """
    )
    
    # 处理参数
    parser.add_argument("--start-date", help="开始日期 (YYYYMMDD格式)")
    parser.add_argument("--end-date", help="结束日期 (YYYYMMDD格式)")
    parser.add_argument("--province-ids", type=int, nargs="+", help="省份ID列表")
    
    # 运行模式
    parser.add_argument("--monitor", action="store_true", help="运行监控模式")
    parser.add_argument("--status", action="store_true", help="显示服务状态")
    
    # 配置文件参数
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    try:
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config
        
        # 创建服务实例
        service = ORCMongoDBMainService(args.config)
        service.start_time = time.time()
        
        # 初始化服务
        await service.initialize()
        
        if args.status:
            # 显示状态
            status = await service.get_service_status()
            print(json.dumps(status, indent=2, ensure_ascii=False))
        elif args.monitor:
            # 运行监控模式
            await service.run_monitoring_loop()
        else:
            # 处理ORC数据
            await service.process_orc_data(
                start_date=args.start_date,
                end_date=args.end_date,
                province_ids=args.province_ids
            )
        
        # 保持运行直到收到停止信号
        while service.is_running and not service.shutdown_requested:
            await asyncio.sleep(1)
        
    except KeyboardInterrupt:
        print("\n接收到中断信号")
    except Exception as e:
        print(f"服务运行失败: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        if 'service' in locals():
            await service.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
