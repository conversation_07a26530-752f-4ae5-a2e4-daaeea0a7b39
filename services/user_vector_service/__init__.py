"""
用户向量服务 - 微服务架构版

微服务架构版本的用户向量计算和存储服务，包含以下微服务：
- MongoDB读取微服务：从MongoDB读取用户数据
- 向量计算微服务：查询Milvus内容向量并计算用户向量
- 向量存储微服务：将用户向量存储到Milvus
- 监控服务：监控所有微服务状态

主要特性：
- 微服务架构，职责分离
- Redis消息队列异步通信
- 批量处理，高性能
- Rich监控面板
- 完全移除is_stored相关操作
- 按省份集合名称筛选

作者: User-DF Team
版本: 2.0.0 (微服务版)
"""

# 导入微服务模块
from . import mongodb_reader_service
from . import vector_processor_service
from . import vector_writer_service
from . import monitoring_service

__version__ = "2.0.0"
__author__ = "User-DF Team"

__all__ = [
    "mongodb_reader_service",
    "vector_processor_service",
    "vector_writer_service",
    "monitoring_service"
]
