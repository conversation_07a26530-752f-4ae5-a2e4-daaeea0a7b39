#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户向量服务微服务演示脚本

演示如何使用用户向量服务的微服务架构
"""

import os
import sys
import time
import json
import requests
import redis
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))


class UserVectorMicroservicesDemo:
    """用户向量服务微服务演示"""
    
    def __init__(self):
        # 服务URL配置
        self.mongodb_reader_url = "http://localhost:8003"
        self.vector_processor_url = "http://localhost:8004"
        self.vector_writer_url = "http://localhost:8005"
        
        # Redis客户端
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        print("🚀 用户向量服务微服务演示")
        print("=" * 50)
    
    def check_services_health(self) -> bool:
        """检查所有服务健康状态"""
        print("🔍 检查微服务健康状态...")
        
        services = [
            ("MongoDB读取微服务", self.mongodb_reader_url),
            ("向量计算微服务", self.vector_processor_url),
            ("向量存储微服务", self.vector_writer_url)
        ]
        
        all_healthy = True
        
        for name, url in services:
            try:
                response = requests.get(f"{url}/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ {name}: 健康 (运行时间: {data.get('uptime', 0):.1f}s)")
                else:
                    print(f"  ❌ {name}: 异常 (HTTP {response.status_code})")
                    all_healthy = False
            except Exception as e:
                print(f"  ❌ {name}: 无法连接 ({str(e)[:50]})")
                all_healthy = False
        
        return all_healthy
    
    def check_queue_status(self):
        """检查消息队列状态"""
        print("\n📊 检查消息队列状态...")
        
        try:
            # 检查Redis队列
            vector_queue_length = self.redis_client.llen("vector_processing_queue")
            storage_queue_length = self.redis_client.llen("vector_storage_queue")
            
            print(f"  📦 向量处理队列长度: {vector_queue_length}")
            print(f"  📦 向量存储队列长度: {storage_queue_length}")
            
            # 检查向量计算服务的队列状态
            try:
                response = requests.get(f"{self.vector_processor_url}/queue/status", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"  🔄 向量计算服务队列状态:")
                    print(f"    输入队列: {data.get('input_queue', {}).get('length', 'unknown')}")
                    print(f"    输出队列: {data.get('output_queue', {}).get('length', 'unknown')}")
                    print(f"    处理状态: {'运行中' if data.get('is_processing', False) else '空闲'}")
            except Exception as e:
                print(f"  ❌ 无法获取向量计算服务队列状态: {e}")
            
            # 检查向量存储服务的队列状态
            try:
                response = requests.get(f"{self.vector_writer_url}/queue/status", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"  💾 向量存储服务队列状态:")
                    print(f"    队列长度: {data.get('queue_length', 'unknown')}")
                    print(f"    处理状态: {'运行中' if data.get('is_processing', False) else '空闲'}")
            except Exception as e:
                print(f"  ❌ 无法获取向量存储服务队列状态: {e}")
                
        except Exception as e:
            print(f"  ❌ 检查队列状态失败: {e}")
    
    def start_user_vector_task(self, prov_id: int, user_limit: Optional[int] = None) -> Optional[str]:
        """启动用户向量计算任务"""
        print(f"\n🎯 启动省份 {prov_id} 的用户向量计算任务...")
        
        try:
            # 构建请求数据
            request_data = {
                "prov_id": prov_id,
                "batch_size": 10000,  # 演示用较小的批次
            }
            
            if user_limit:
                request_data["user_limit"] = user_limit
            
            # 发送请求
            response = requests.post(
                f"{self.mongodb_reader_url}/read",
                json=request_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get("task_id")
                print(f"  ✅ 任务已启动: {task_id}")
                print(f"  📝 状态: {data.get('status')}")
                print(f"  💬 消息: {data.get('message')}")
                return task_id
            else:
                print(f"  ❌ 启动任务失败: HTTP {response.status_code}")
                print(f"  📄 响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"  ❌ 启动任务异常: {e}")
            return None
    
    def monitor_task_progress(self, task_id: str, max_wait_time: int = 300):
        """监控任务进度"""
        print(f"\n📈 监控任务进度: {task_id}")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                # 获取任务状态
                response = requests.get(f"{self.mongodb_reader_url}/task/{task_id}", timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get("status")
                    processed_users = data.get("processed_users", 0)
                    total_users = data.get("total_users", 0)
                    
                    print(f"  📊 任务状态: {status}")
                    if total_users > 0:
                        progress = (processed_users / total_users) * 100
                        print(f"  📈 进度: {processed_users}/{total_users} ({progress:.1f}%)")
                    
                    if status in ["completed", "failed"]:
                        if status == "completed":
                            print(f"  ✅ 任务完成!")
                        else:
                            error_msg = data.get("error_message", "未知错误")
                            print(f"  ❌ 任务失败: {error_msg}")
                        break
                else:
                    print(f"  ❌ 获取任务状态失败: HTTP {response.status_code}")
                
                # 等待一段时间后再检查
                time.sleep(5)
                
            except Exception as e:
                print(f"  ❌ 监控任务异常: {e}")
                time.sleep(5)
        else:
            print(f"  ⏰ 监控超时 ({max_wait_time}秒)")
    
    def show_service_stats(self):
        """显示服务统计信息"""
        print("\n📊 服务统计信息:")
        
        services = [
            ("MongoDB读取微服务", self.mongodb_reader_url),
            ("向量计算微服务", self.vector_processor_url),
            ("向量存储微服务", self.vector_writer_url)
        ]
        
        for name, url in services:
            try:
                response = requests.get(f"{url}/stats", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    stats = data.get("stats", {})
                    
                    print(f"\n  📈 {name}:")
                    for key, value in stats.items():
                        if isinstance(value, (int, float)):
                            print(f"    {key}: {value}")
                else:
                    print(f"  ❌ {name}: 无法获取统计信息 (HTTP {response.status_code})")
            except Exception as e:
                print(f"  ❌ {name}: 获取统计信息异常 ({str(e)[:50]})")
    
    def run_demo(self, prov_id: int = 100, user_limit: Optional[int] = 1000):
        """运行完整演示"""
        print(f"🎬 开始用户向量服务微服务演示")
        print(f"📍 省份ID: {prov_id}")
        if user_limit:
            print(f"👥 用户限制: {user_limit}")
        print()
        
        # 1. 检查服务健康状态
        if not self.check_services_health():
            print("\n❌ 部分服务不健康，请检查服务状态")
            return False
        
        # 2. 检查队列状态
        self.check_queue_status()
        
        # 3. 启动任务
        task_id = self.start_user_vector_task(prov_id, user_limit)
        if not task_id:
            print("\n❌ 任务启动失败")
            return False
        
        # 4. 监控任务进度
        self.monitor_task_progress(task_id, max_wait_time=120)  # 2分钟超时
        
        # 5. 显示最终统计
        self.show_service_stats()
        
        # 6. 最终队列状态
        self.check_queue_status()
        
        print("\n🎉 演示完成!")
        return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="用户向量服务微服务演示")
    parser.add_argument("--prov-id", type=int, default=100, help="省份ID")
    parser.add_argument("--user-limit", type=int, help="用户数量限制")
    parser.add_argument("--health-only", action="store_true", help="只检查健康状态")
    parser.add_argument("--stats-only", action="store_true", help="只显示统计信息")
    
    args = parser.parse_args()
    
    try:
        demo = UserVectorMicroservicesDemo()
        
        if args.health_only:
            demo.check_services_health()
            demo.check_queue_status()
        elif args.stats_only:
            demo.show_service_stats()
            demo.check_queue_status()
        else:
            demo.run_demo(args.prov_id, args.user_limit)
    
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
