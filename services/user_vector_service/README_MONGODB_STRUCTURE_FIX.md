# user_vector_service MongoDB数据结构修复说明

## 修复概述

由于MongoDB数据结构发生了调整，user_vector_service中的相关功能需要进行更新修复。本文档详细说明了修复的内容和变更。

## 数据结构变更对比

### 旧数据结构
```json
{
  "_id": 123456789,
  "pid_groups": [
    {
      "timestamp_days": 19723,
      "pids": [1001, 1002, 1003]
    }
  ],
  "vector_status": {
    "is_stored": false,
    "stored_at_days": null
  },
  "prov_id": 200
}
```

### 新数据结构
```json
{
  "_id": 123456789,
  "pids": [1001, 1002, 1003],
  "pid_count": 3,
  "updated_days": 19723
}
```

## 主要变更内容

### 1. mongodb_reader_service 修复

#### 修复内容：
1. **集合命名模式更新**
   - 旧：`user_pid_records_optimized_{prov_id}`
   - 新：`user_pid_records_{prov_id}`

2. **投影字段更新**
   ```yaml
   # 旧投影字段
   projection:
     _id: 1
     pid_groups: 1
     prov_id: 1
     updated_days: 1
   
   # 新投影字段
   projection:
     _id: 1
     pids: 1
     pid_count: 1
     updated_days: 1
   ```

3. **MongoDB操作类实例化修复**
   - 修复了MongoDBOperations构造函数参数错误
   - 为每个集合创建独立的操作实例

#### 修复的文件：
- `services/user_vector_service/mongodb_reader_service/service.py`
- `configs/user_vector_service/mongodb_reader_service/development.yaml`

### 2. vector_processor_service 修复

#### 修复内容：
1. **PID提取逻辑更新**
   ```python
   # 旧逻辑：从pid_groups中提取PID
   for user in users:
       uid = user["_id"]
       pid_groups = user.get("pid_groups", [])
       user_pids = []
       for group in pid_groups:
           pids = group.get("pids", [])
           user_pids.extend(pids)
   
   # 新逻辑：直接从pids字段读取
   for user in users:
       uid = user["_id"]
       user_pids = user.get("pids", [])
   ```

2. **省份ID处理更新**
   - 新数据结构中prov_id通过集合名称区分，不再存储在文档中
   - 通过函数参数传递prov_id信息

#### 修复的文件：
- `services/user_vector_service/vector_processor_service/service.py`

### 3. 配置文件更新

#### 修复内容：
1. **集合命名模式配置**
   ```yaml
   # 开发环境配置
   mongodb:
     collection_pattern: "user_pid_records_{prov_id}"
   ```

#### 修复的文件：
- `configs/user_vector_service/mongodb_reader_service/development.yaml`

## 兼容性说明

### 向后兼容性
- ❌ **不保证向后兼容**：新实现专门适配新的数据结构
- ✅ **数据迁移**：旧数据可通过数据转换脚本迁移到新结构

### 数据转换示例
```python
# 旧数据到新数据的转换逻辑
def convert_old_to_new(old_data):
    # 提取所有PID
    all_pids = []
    for group in old_data.get("pid_groups", []):
        all_pids.extend(group.get("pids", []))
    
    # 构建新数据结构
    new_data = {
        "_id": old_data["_id"],
        "pids": all_pids,
        "pid_count": len(all_pids),
        "updated_days": int(time.time()) // 86400
    }
    
    return new_data
```

## 测试验证

### 核心修复测试
运行以下命令验证修复结果：
```bash
python3 services/user_vector_service/test_core_fixes.py
```

### 测试覆盖范围
1. ✅ **数据结构兼容性**：验证新数据结构的字段和类型
2. ✅ **PID提取逻辑**：验证从新结构中正确提取PID
3. ✅ **集合命名模式**：验证新的集合命名规则
4. ✅ **数据字段映射**：验证旧数据到新数据的转换
5. ✅ **MongoDB投影字段**：验证查询字段的正确性

### 测试结果
```
=== 测试结果汇总 ===
数据结构兼容性: ✓ 通过
PID提取逻辑: ✓ 通过
集合命名模式: ✓ 通过
数据字段映射: ✓ 通过
MongoDB投影字段: ✓ 通过

总计: 5/5 个测试通过
🎉 所有核心修复测试通过！
```

## 部署注意事项

### 1. 数据库准备
- 确保新的集合结构已创建
- 验证数据已按新格式存储
- 检查集合索引配置

### 2. 配置更新
- 更新所有环境的配置文件
- 验证集合命名模式配置
- 检查MongoDB连接参数

### 3. 服务重启
- 按顺序重启相关微服务
- 监控服务启动日志
- 验证服务间通信正常

### 4. 功能验证
- 测试用户数据读取功能
- 验证PID提取和向量计算
- 检查数据处理流程完整性

## 影响范围

### 直接影响的服务
1. **mongodb_reader_service**：数据读取逻辑
2. **vector_processor_service**：PID提取和向量计算

### 间接影响的服务
1. **vector_writer_service**：接收处理结果（无需修改）
2. **monitoring_service**：监控数据结构（可能需要调整）

## 回滚方案

如果需要回滚到旧版本：

1. **代码回滚**
   ```bash
   git checkout <previous_commit>
   ```

2. **配置回滚**
   - 恢复旧的集合命名模式
   - 恢复旧的投影字段配置

3. **数据回滚**
   - 使用备份数据恢复旧格式
   - 或运行数据转换脚本

## 性能影响

### 预期改进
1. **查询性能**：简化的数据结构减少查询复杂度
2. **存储效率**：去除冗余字段，减少存储空间
3. **处理速度**：直接字段访问，提高处理效率

### 监控指标
- MongoDB查询响应时间
- 数据处理吞吐量
- 内存使用情况
- 错误率统计

## 故障排除

### 常见问题

1. **集合不存在错误**
   - 检查集合命名是否正确
   - 验证省份ID参数传递

2. **字段不存在错误**
   - 确认数据已按新格式存储
   - 检查投影字段配置

3. **PID提取失败**
   - 验证pids字段数据格式
   - 检查数据类型转换

### 调试方法
1. 启用详细日志记录
2. 使用测试脚本验证功能
3. 检查MongoDB查询语句
4. 监控服务性能指标

## 总结

本次修复成功适配了新的MongoDB数据结构，主要变更包括：

1. ✅ **集合命名规则更新**：适配新的命名模式
2. ✅ **数据字段映射**：从复杂结构简化为扁平结构
3. ✅ **PID提取逻辑**：直接字段访问替代嵌套遍历
4. ✅ **配置文件同步**：保持配置与代码一致
5. ✅ **测试验证完整**：确保修复质量

修复后的user_vector_service能够正确处理新的数据结构，保持了原有的功能完整性，并提升了处理效率。
