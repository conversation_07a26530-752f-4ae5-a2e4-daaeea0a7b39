#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量计算微服务主入口

提供HTTP API接口和后台队列处理
"""

import os
import sys
import asyncio
import argparse
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExceptionHandler
from .service import VectorProcessorService


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="向量计算微服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动服务（使用默认配置）
  python3 services/user_vector_service/vector_processor_service/main.py

  # 指定主机和端口
  python3 services/user_vector_service/vector_processor_service/main.py --host 0.0.0.0 --port 8004

  # 指定配置文件
  python3 services/user_vector_service/vector_processor_service/main.py --config configs/user_vector_service/vector_processor_service/development.yaml
        """
    )

    # 服务配置参数
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8004, help="服务端口")

    # 配置文件参数
    parser.add_argument("--config",
                       default="configs/user_vector_service/vector_processor_service/development.yaml",
                       help="配置文件路径")

    # 日志参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="日志级别")

    return parser


def validate_arguments(args):
    """验证命令行参数"""
    if args.port < 1 or args.port > 65535:
        raise ValueError("端口号必须在1-65535之间")


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()

        # 验证参数
        validate_arguments(args)

        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config
        if args.log_level:
            os.environ['USER_DF_LOG_LEVEL'] = args.log_level

        # 初始化配置管理器
        config_manager = ConfigManager()
        logger = Logger.get_logger(__name__)

        logger.info("=== 向量计算微服务启动 ===")

        # 创建服务实例
        service = VectorProcessorService(config_manager)
        await service.initialize()

        # 启动后台队列处理
        asyncio.create_task(service.start_queue_processing())

        # 启动服务
        logger.info(f"服务启动在 {args.host}:{args.port}")

        import uvicorn
        config = uvicorn.Config(
            service.app,
            host=args.host,
            port=args.port,
            log_level="info"
        )
        server = uvicorn.Server(config)

        # 启动服务器
        await server.serve()

    except Exception as e:
        logger = Logger.get_logger("VectorProcessorService")
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
